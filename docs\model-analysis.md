# 模型类型分析报告

## 现有模型类型总结

基于 `app/controller/ai.js` 中的 `redisLockMap` 和 `modelMap` 分析，系统支持以下模型类型：

### 模型映射表

| 模型ID | Redis锁键名 | 服务方法 | 描述 | 队列名称 |
|--------|-------------|----------|------|----------|
| 2 | sili | tuili/index | 硅基流动 | sili-queue |
| 3 | zhihuwenda | askQuestion3 | 知乎问答 | zhihu-queue |
| 4 | zhihuwenda1 | askQuestion3 | 知乎问答1 | zhihu1-queue |
| 5 | zhihuwenda2 | askQuestion3 | 知乎问答2 | zhihu2-queue |
| 6 | zhihuwenda2 | askQuestion3 | 知乎问答2(备用) | zhihu2-alt-queue |
| 7 | zhihuwenda3 | askQuestion3 | 知乎问答3 | zhihu3-queue |
| 8 | zhihuwenda4 | askQuestion3 | 知乎问答4 | zhihu4-queue |
| 9 | zhihuwenda5 | askQuestion3 | 知乎问答5 | zhihu5-queue |
| 11 | zhihuwenda6 | askQuestion3 | 知乎问答6 | zhihu6-queue |
| 12 | zhihuwenda7 | askQuestion3 | 知乎问答7 | zhihu7-queue |

### 特殊模型

- **模型10**: 特殊处理模型，不设置Redis锁，用于特定的allcateid处理
- **模型6**: 与模型5共享相同的Redis锁键名，但错误消息不同

### 锁机制分析

#### 当前锁逻辑
1. **检查锁状态**: `+isLocked === 1` 表示正在处理
2. **设置锁**: 处理开始时设置为 `'1'`
3. **释放锁**: 处理完成时设置为 `'2'`
4. **特殊状态**: `+isLocked === 3` 也被视为锁定状态

#### 锁键名规律
- 模型2: `sili` (硅基流动)
- 模型3-12: `zhihuwenda` + 数字后缀 (知乎问答系列)

### BullMQ迁移策略

#### 队列设计原则
1. **一对一映射**: 每个模型对应一个独立队列
2. **命名规范**: `{service}-queue` 格式
3. **并发控制**: 每个队列 concurrency=1，替代Redis锁
4. **错误处理**: 统一的重试和错误处理机制

#### 队列配置
```javascript
const queueConfigs = {
  sili: { name: 'sili-queue', concurrency: 1 },
  zhihu: { name: 'zhihu-queue', concurrency: 1 },
  zhihu1: { name: 'zhihu1-queue', concurrency: 1 },
  // ... 其他队列
};
```

#### 迁移优势
1. **消除锁竞争**: 每个队列独立处理，无需Redis锁
2. **提高可靠性**: 作业持久化，进程重启不丢失
3. **更好监控**: BullMQ提供丰富的监控功能
4. **自动重试**: 内置重试机制，替代手动错误处理

### 数据结构定义

#### 作业数据结构
```javascript
{
  per: number,        // 处理数量限制
  type: string,       // 分类类型
  type1: string,      // 备用分类类型
  model: number,      // 模型ID
  up: boolean,        // 是否更新模式
}
```

#### 处理结果结构
```javascript
{
  success: boolean,
  data: string,       // AI处理结果
  status: number,     // 状态码
  itemId: number,     // 处理的记录ID
  executionTime: number, // 执行时间(毫秒)
}
```

## 下一步行动

1. ✅ 创建BullMQ配置文件
2. 🔄 设计队列命名策略
3. ⏳ 定义作业类型和数据结构
4. ⏳ 设计并发控制策略
