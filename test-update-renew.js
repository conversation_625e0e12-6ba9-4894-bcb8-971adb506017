/**
 * 测试update_renew专门处理ds='重新更新'记录的功能
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:7001';

async function testUpdateRenew() {
  console.log('🔄 测试update_renew专门处理重新更新记录...\n');

  try {
    // 1. 测试缺少model参数
    console.log('1️⃣ 测试缺少model参数');
    try {
      const response1 = await axios.get(`${BASE_URL}/update_renew?per=1`);
      console.log('   响应:', response1.data);
    } catch (error) {
      console.log('   错误:', error.response?.data || error.message);
    }

    // 2. 测试无效model参数
    console.log('\n2️⃣ 测试无效model参数');
    try {
      const response2 = await axios.get(`${BASE_URL}/update_renew?per=1&model=999`);
      console.log('   响应:', response2.data);
    } catch (error) {
      console.log('   错误:', error.response?.data || error.message);
    }

    // 3. 测试正常调用 - 模型3
    console.log('\n3️⃣ 测试正常调用 - 模型3');
    try {
      const response3 = await axios.get(`${BASE_URL}/update_renew?per=1&model=3`);
      console.log('   响应:', response3.data);
      
      if (response3.data.success) {
        console.log(`   ✅ 作业已添加到队列: ${response3.data.queueName}`);
        console.log(`   📋 作业ID: ${response3.data.jobId}`);
        console.log(`   🔄 renewOnly: ${response3.data.renewOnly}`);
      }
    } catch (error) {
      console.log('   错误:', error.response?.data || error.message);
    }

    // 4. 测试正常调用 - 模型2
    console.log('\n4️⃣ 测试正常调用 - 模型2');
    try {
      const response4 = await axios.get(`${BASE_URL}/update_renew?per=2&model=2`);
      console.log('   响应:', response4.data);
      
      if (response4.data.success) {
        console.log(`   ✅ 作业已添加到队列: ${response4.data.queueName}`);
        console.log(`   📋 作业ID: ${response4.data.jobId}`);
        console.log(`   🔄 renewOnly: ${response4.data.renewOnly}`);
      }
    } catch (error) {
      console.log('   错误:', error.response?.data || error.message);
    }

    // 5. 测试lock参数
    console.log('\n5️⃣ 测试lock参数');
    try {
      const response5 = await axios.get(`${BASE_URL}/update_renew?lock=true`);
      console.log('   响应:', response5.data);
      console.log('   ✅ lock参数处理正常');
    } catch (error) {
      console.log('   错误:', error.response?.data || error.message);
    }

    // 6. 检查BullMQ状态
    console.log('\n6️⃣ 检查BullMQ状态');
    try {
      const response6 = await axios.get(`${BASE_URL}/bullmqStatus`);
      console.log('   BullMQ配置:', response6.data.config);
      console.log('   Workers状态:', Object.keys(response6.data.workers));
    } catch (error) {
      console.log('   错误:', error.response?.data || error.message);
    }

    console.log('\n🎉 update_renew测试完成！');
    console.log('\n📝 测试总结:');
    console.log('   - update_renew现在专门处理ds="重新更新"的记录');
    console.log('   - 移除了type、type1、up、ss等不必要的参数');
    console.log('   - 简化了参数验证和作业数据结构');
    console.log('   - 作业数据中包含renewOnly=true标记');
    console.log('   - BullMQ处理器会根据renewOnly标记执行专门的SQL查询');

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
  }
}

async function testDatabaseQuery() {
  console.log('\n🗄️ 测试数据库查询逻辑...\n');

  // 这里可以添加直接的数据库查询测试
  console.log('SQL查询逻辑:');
  console.log('   原查询: 复杂的条件判断和多个查询');
  console.log('   新查询: SELECT * FROM fbsy WHERE ds = "重新更新" ORDER BY id DESC LIMIT {per}');
  console.log('   优势: 查询更简单、更直接、性能更好');
}

async function runAllTests() {
  console.log('🚀 开始update_renew功能测试\n');
  
  await testUpdateRenew();
  await testDatabaseQuery();
  
  console.log('\n✅ 所有测试完成！');
}

// 运行测试
runAllTests().catch(error => {
  console.error('💥 测试运行异常:', error);
  process.exit(1);
});
