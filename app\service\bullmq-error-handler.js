'use strict';

/**
 * BullMQ错误处理服务
 * 提供统一的错误处理和重试机制
 */

const Service = require('egg').Service;

class BullMQErrorHandlerService extends Service {

  /**
   * 处理作业错误
   * @param {object} job - BullMQ作业对象
   * @param {Error} error - 错误对象
   * @param {string} queueKey - 队列键名
   */
  async handleJobError(job, error, queueKey) {
    const { ctx, app, logger } = this;

    try {
      // 记录错误日志
      logger.error(`作业处理失败: ${job.id}, 队列: ${queueKey}`, {
        jobId: job.id,
        queueKey,
        error: error.message,
        stack: error.stack,
        jobData: job.data,
        attemptsMade: job.attemptsMade,
        attemptsTotal: job.opts.attempts,
      });

      // 判断是否为最后一次重试
      const isLastAttempt = job.attemptsMade >= job.opts.attempts;

      if (isLastAttempt) {
        // 最后一次重试失败，执行最终失败处理
        await this.handleFinalFailure(job, error, queueKey);
      } else {
        // 还有重试机会，记录重试信息
        await this.handleRetryAttempt(job, error, queueKey);
      }

      // 发送错误通知
      await this.sendErrorNotification(job, error, queueKey, isLastAttempt);

    } catch (handlerError) {
      logger.error('错误处理器本身出错:', handlerError);
    }
  }

  /**
   * 处理最终失败
   * @param {object} job - BullMQ作业对象
   * @param {Error} error - 错误对象
   * @param {string} queueKey - 队列键名
   */
  async handleFinalFailure(job, error, queueKey) {
    const { ctx, logger } = this;

    try {
      // 如果作业数据中包含item ID，尝试恢复数据库状态
      if (job.data.itemId) {
        await ctx.service.xr.query(
          `UPDATE fbsy SET ds = '处理失败', tag = tag + 1 WHERE id = ${job.data.itemId}`
        );
        logger.info(`已将item ${job.data.itemId} 标记为处理失败`);
      }

      // 记录到失败统计
      await this.recordFailureStats(job, error, queueKey);

    } catch (recoveryError) {
      logger.error('最终失败处理出错:', recoveryError);
    }
  }

  /**
   * 处理重试尝试
   * @param {object} job - BullMQ作业对象
   * @param {Error} error - 错误对象
   * @param {string} queueKey - 队列键名
   */
  async handleRetryAttempt(job, error, queueKey) {
    const { logger } = this;

    const nextAttempt = job.attemptsMade + 1;
    const totalAttempts = job.opts.attempts;
    
    logger.info(`作业 ${job.id} 将进行第 ${nextAttempt}/${totalAttempts} 次重试`);

    // 计算重试延迟
    const delay = this.calculateRetryDelay(job.attemptsMade, job.opts.backoff);
    logger.info(`重试延迟: ${delay}ms`);
  }

  /**
   * 计算重试延迟
   * @param {number} attemptsMade - 已尝试次数
   * @param {object} backoffConfig - 退避配置
   * @returns {number} 延迟毫秒数
   */
  calculateRetryDelay(attemptsMade, backoffConfig) {
    if (!backoffConfig || backoffConfig.type !== 'exponential') {
      return 2000; // 默认2秒
    }

    const baseDelay = backoffConfig.delay || 2000;
    return Math.min(baseDelay * Math.pow(2, attemptsMade), 60000); // 最大60秒
  }

  /**
   * 发送错误通知
   * @param {object} job - BullMQ作业对象
   * @param {Error} error - 错误对象
   * @param {string} queueKey - 队列键名
   * @param {boolean} isLastAttempt - 是否为最后一次尝试
   */
  async sendErrorNotification(job, error, queueKey, isLastAttempt) {
    const { ctx } = this;

    try {
      const status = isLastAttempt ? '最终失败' : '重试中';
      const message = `【BullMQ错误】${status}
作业ID: ${job.id}
队列: ${queueKey}
尝试次数: ${job.attemptsMade}/${job.opts.attempts}
错误信息: ${error.message}
作业数据: ${JSON.stringify(job.data, null, 2)}`;

      // 根据错误严重程度选择通知方式
      if (isLastAttempt) {
        await ctx.service.feishu.fs(message); // 最终失败用fs
      } else {
        await ctx.service.feishu.fs3(message); // 重试用fs3
      }

    } catch (notifyError) {
      this.logger.error('发送错误通知失败:', notifyError);
    }
  }

  /**
   * 记录失败统计
   * @param {object} job - BullMQ作业对象
   * @param {Error} error - 错误对象
   * @param {string} queueKey - 队列键名
   */
  async recordFailureStats(job, error, queueKey) {
    const { app, logger } = this;

    try {
      // 可以在这里记录到数据库或Redis进行统计分析
      const failureKey = `bullmq:failures:${queueKey}`;
      const failureData = {
        jobId: job.id,
        error: error.message,
        timestamp: Date.now(),
        jobData: job.data,
      };

      await app.redis.lpush(failureKey, JSON.stringify(failureData));
      await app.redis.ltrim(failureKey, 0, 99); // 只保留最近100条失败记录

      logger.info(`失败统计已记录: ${failureKey}`);

    } catch (statsError) {
      logger.error('记录失败统计出错:', statsError);
    }
  }

  /**
   * 获取队列失败统计
   * @param {string} queueKey - 队列键名
   * @returns {Array} 失败记录列表
   */
  async getFailureStats(queueKey) {
    const { app } = this;

    try {
      const failureKey = `bullmq:failures:${queueKey}`;
      const failures = await app.redis.lrange(failureKey, 0, -1);
      
      return failures.map(failure => {
        try {
          return JSON.parse(failure);
        } catch (e) {
          return { error: 'Failed to parse failure record', raw: failure };
        }
      });

    } catch (error) {
      this.logger.error('获取失败统计出错:', error);
      return [];
    }
  }

  /**
   * 清理过期的失败记录
   * @param {string} queueKey - 队列键名
   * @param {number} maxAge - 最大保留时间(毫秒)
   */
  async cleanupFailureStats(queueKey, maxAge = 24 * 60 * 60 * 1000) {
    const { app, logger } = this;

    try {
      const failures = await this.getFailureStats(queueKey);
      const now = Date.now();
      const validFailures = failures.filter(failure => {
        return (now - failure.timestamp) < maxAge;
      });

      if (validFailures.length !== failures.length) {
        const failureKey = `bullmq:failures:${queueKey}`;
        await app.redis.del(failureKey);
        
        for (const failure of validFailures) {
          await app.redis.lpush(failureKey, JSON.stringify(failure));
        }

        logger.info(`清理过期失败记录: ${queueKey}, 删除 ${failures.length - validFailures.length} 条`);
      }

    } catch (error) {
      logger.error('清理失败记录出错:', error);
    }
  }

  /**
   * 判断错误是否可重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否可重试
   */
  isRetryableError(error) {
    // 定义不可重试的错误类型
    const nonRetryableErrors = [
      'ValidationError',
      'AuthenticationError',
      'PermissionError',
      'InvalidDataError',
    ];

    // 检查错误类型
    if (nonRetryableErrors.includes(error.name)) {
      return false;
    }

    // 检查错误消息中的关键词
    const nonRetryableMessages = [
      '参数错误',
      '数据格式错误',
      '权限不足',
      '认证失败',
    ];

    const errorMessage = error.message.toLowerCase();
    for (const message of nonRetryableMessages) {
      if (errorMessage.includes(message.toLowerCase())) {
        return false;
      }
    }

    return true;
  }
}

module.exports = BullMQErrorHandlerService;
