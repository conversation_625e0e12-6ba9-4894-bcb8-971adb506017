'use strict';

/**
 * BullMQ Workers
 * 为每个模型创建独立的Worker处理器
 */

const { Worker } = require('bullmq');
const { getBullMQConfig, getAllQueueConfigs } = require('../../config/bullmq');

class BullMQWorkers {
  constructor(app) {
    this.app = app;
    this.workers = new Map();
    this.config = getBullMQConfig(app.config.env);
    this.queueConfigs = getAllQueueConfigs();
  }

  /**
   * 启动所有Workers
   */
  async start() {
    const { app } = this;

    try {
      // 为每个队列创建Worker
      for (const [queueKey, queueConfig] of Object.entries(this.queueConfigs)) {
        const worker = new Worker(
          queueConfig.name,
          async (job) => {
            return await this.processJob(job, queueKey);
          },
          {
            connection: this.config.connection,
            concurrency: queueConfig.concurrency,
            removeOnComplete: queueConfig.removeOnComplete,
            removeOnFail: queueConfig.removeOnFail,
          },
        );

        // 设置事件监听器
        this.setupWorkerEvents(worker, queueKey);

        this.workers.set(queueKey, worker);
        app.logger.info(`BullMQ Worker启动: ${queueConfig.name} (${queueKey})`);
      }

      app.logger.info(`所有BullMQ Workers已启动，共${this.workers.size}个`);
    } catch (error) {
      app.logger.error('启动BullMQ Workers失败:', error);
      throw error;
    }
  }

  /**
   * 处理作业
   * @param {object} job - BullMQ作业对象
   * @param {string} queueKey - 队列键名
   */
  async processJob(job, queueKey) {
    const { app } = this;
    const startTime = Date.now();

    try {
      app.logger.info(`开始处理作业: ${job.id}, 队列: ${queueKey}, 数据:`, job.data);

      // 创建临时上下文
      const ctx = app.createAnonymousContext();

      // 调用处理器服务
      const result = await ctx.service.bullmqProcessor.processItem(job.data);

      const elapsed = Date.now() - startTime;
      app.logger.info(`作业处理完成: ${job.id}, 耗时: ${elapsed}ms`);

      // 发送成功通知
      await ctx.service.feishu.fs3(
        `【BullMQ】作业处理成功 - ID: ${job.id}, 队列: ${queueKey}, 耗时: ${elapsed}ms`,
      );

      return result;
    } catch (error) {
      const elapsed = Date.now() - startTime;
      app.logger.error(
        `作业处理失败: ${job.id}, 队列: ${queueKey}, 耗时: ${elapsed}ms, 错误:`,
        error,
      );

      // 使用错误处理服务
      try {
        const ctx = app.createAnonymousContext();
        await ctx.service.bullmqErrorHandler.handleJobError(job, error, queueKey);
      } catch (handlerError) {
        app.logger.error('错误处理器失败:', handlerError);
      }

      throw error;
    }
  }

  /**
   * 设置Worker事件监听器
   * @param {Worker} worker - Worker实例
   * @param {string} queueKey - 队列键名
   */
  setupWorkerEvents(worker, queueKey) {
    const { app } = this;

    // 作业完成事件
    worker.on('completed', (job, result) => {
      app.logger.info(`作业完成: ${job.id}, 队列: ${queueKey}`);
    });

    // 作业失败事件
    worker.on('failed', (job, err) => {
      app.logger.error(`作业失败: ${job?.id}, 队列: ${queueKey}, 错误: ${err.message}`);
    });

    // 作业进度事件
    worker.on('progress', (job, progress) => {
      app.logger.info(`作业进度: ${job.id}, 队列: ${queueKey}, 进度: ${progress}%`);
    });

    // Worker错误事件
    worker.on('error', (err) => {
      app.logger.error(`Worker错误, 队列: ${queueKey}, 错误: ${err.message}`);
    });

    // Worker关闭事件
    worker.on('closed', () => {
      app.logger.info(`Worker关闭: ${queueKey}`);
    });

    // Worker准备就绪事件
    worker.on('ready', () => {
      app.logger.info(`Worker准备就绪: ${queueKey}`);
    });
  }

  /**
   * 停止所有Workers
   */
  async stop() {
    const { app } = this;

    try {
      const stopPromises = [];

      for (const [queueKey, worker] of this.workers) {
        app.logger.info(`正在停止Worker: ${queueKey}`);
        stopPromises.push(worker.close());
      }

      await Promise.all(stopPromises);
      this.workers.clear();

      app.logger.info('所有BullMQ Workers已停止');
    } catch (error) {
      app.logger.error('停止BullMQ Workers失败:', error);
      throw error;
    }
  }

  /**
   * 获取Worker状态
   */
  getStatus() {
    const status = {};

    for (const [queueKey, worker] of this.workers) {
      status[queueKey] = {
        isRunning: worker.isRunning(),
        isPaused: worker.isPaused(),
        isClosed: worker.isClosed(),
      };
    }

    return status;
  }

  /**
   * 暂停指定队列的Worker
   * @param {string} queueKey - 队列键名
   */
  async pauseWorker(queueKey) {
    const worker = this.workers.get(queueKey);
    if (worker) {
      await worker.pause();
      this.app.logger.info(`Worker已暂停: ${queueKey}`);
    } else {
      throw new Error(`Worker不存在: ${queueKey}`);
    }
  }

  /**
   * 恢复指定队列的Worker
   * @param {string} queueKey - 队列键名
   */
  async resumeWorker(queueKey) {
    const worker = this.workers.get(queueKey);
    if (worker) {
      await worker.resume();
      this.app.logger.info(`Worker已恢复: ${queueKey}`);
    } else {
      throw new Error(`Worker不存在: ${queueKey}`);
    }
  }

  /**
   * 获取指定Worker
   * @param {string} queueKey - 队列键名
   */
  getWorker(queueKey) {
    return this.workers.get(queueKey);
  }

  /**
   * 获取所有Workers
   */
  getAllWorkers() {
    return Array.from(this.workers.values());
  }
}

module.exports = BullMQWorkers;
