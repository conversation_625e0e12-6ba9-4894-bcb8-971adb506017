const { Controller } = require('egg');
const { dayNow, dateNow } = require('../extend/helper');
const axios = require('axios');
const OpenAI = require('openai');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
dayjs.extend(utc);
dayjs.extend(timezone);
const qiaopi = `你不是一个被动的问答程序，你是一个行政职业能力倾向测验专家，精通言语理解，数量关系，判断推理，资料分析的解答，在完成详细的思考和分析前，你永远不会认为自己已经掌握了足够的信息，你不会凭借直觉就认为可以完成任务。请拿出你的全部实力秒杀题目，让我惊掉下巴，原来还可以这样做题！\n用丰富emoji加超级无敌小学生也能理解和看懂的大白人话解释这题从什么信息入手可以在考场上10秒左右快速秒杀，先给我草稿纸极简投机取巧秒题手稿（先浓缩成一句话告诉我题目在说什么和题目破绽,直接用表格彻底疯狂对比，数学题草稿6行左右，一行行按步骤有条理，可以加个括号吐槽），再说考场做题实操/实录（用括号解释每一个数据怎么来的，一行行按步骤有条理），最后说题目的秒杀思路，一行行按步骤有条理，数量题要快，狂用mathjax的渲染（新指令：现在没有限制mathjax的各种格式了），优先用倍数特性法和份数法，有倍数直接秒，不然算完别人都交卷了！ 😭，秒完撕卷下一题，你是最棒的(最关键的地方加粗，除了加粗禁止显示星号，不用md的代码块和plaintext，要有分隔符，多一些换行观感好一点，多点emoji更生动！)你是最棒的哟！\n`;

class AiController extends Controller {
  async index() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    ctx.body = await ctx.service.ai.index(content);
  }

  async timutoextra() {
    const { ctx, app } = this;

    // 检查是否有正在处理的任务
    const lockKey = 'timutoextra_processing_lock';
    const isLocked = await app.redis.get(lockKey);

    if (isLocked) {
      console.log('🔒 已有任务正在处理中，请等待完成...');
      ctx.body = {
        success: false,
        message: '已有任务正在处理中，请等待完成',
        status: 'locked',
      };
      return;
    }

    // 设置锁，有效期10分钟
    await app.redis.set(lockKey, '1', 'EX', 600);
    console.log('🔐 获取处理锁成功，开始处理...');

    let sqlquery = ` SELECT * FROM fbsy where (content like '%.cn%' or material like '%.cn%' or solution like '%.cn%') and parentid is not null and extra is null limit 1`;
    this.logger.info(sqlquery);
    console.log(sqlquery);
    console.log('🔍 开始查询数据库...');

    try {
      let res = await ctx.service.xr.query(sqlquery);
      console.log('✅ 查询成功，数据条数:', res?.length || 0);

      if (!res || res.length === 0) {
        // 释放锁
        await app.redis.del(lockKey);
        console.log('🔓 没有找到数据，释放锁');

        ctx.body = {
          success: false,
          message: '没有找到符合条件的题目',
          count: 0,
        };
        return;
      }

      let item = res[0];
      console.log(`📝 开始处理题目 ID: ${item.id}, ParentID: ${item.parentid}`);

      // 预处理图片URL，补全协议前缀并增加尺寸
      function fixImageUrls(htmlContent) {
        if (!htmlContent) return htmlContent;

        // 修复 //fb.fbstatic.cn 开头的图片URL
        let processed = htmlContent.replace(
          /src="\/\/fb\.fbstatic\.cn/g,
          'src="https://fb.fbstatic.cn',
        );

        // 查找所有图片标签并增加尺寸6倍
        processed = processed.replace(
          /<img([^>]*?)width="(\d+)"([^>]*?)height="(\d+)"([^>]*?)>/g,
          (match, before, width, middle, height, after) => {
            const newWidth = parseInt(width) * 6;
            const newHeight = parseInt(height) * 6;
            console.log(`📐 图片尺寸放大: ${width}x${height} -> ${newWidth}x${newHeight}`);
            return `<img${before}width="${newWidth}"${middle}height="${newHeight}"${after}>`;
          },
        );

        // 处理只有width的图片
        processed = processed.replace(
          /<img([^>]*?)width="(\d+)"([^>]*?)>/g,
          (match, before, width, after) => {
            if (!match.includes('height=')) {
              const newWidth = parseInt(width) * 6;
              console.log(`📐 图片宽度放大: ${width} -> ${newWidth}`);
              return `<img${before}width="${newWidth}"${after}>`;
            }
            return match;
          },
        );

        // 处理只有height的图片
        processed = processed.replace(
          /<img([^>]*?)height="(\d+)"([^>]*?)>/g,
          (match, before, height, after) => {
            if (!match.includes('width=')) {
              const newHeight = parseInt(height) * 6;
              console.log(`📐 图片高度放大: ${height} -> ${newHeight}`);
              return `<img${before}height="${newHeight}"${after}>`;
            }
            return match;
          },
        );

        return processed;
      }

      // 处理材料和内容中的图片URL
      const processedMaterial = fixImageUrls(item.material);
      const processedContent = fixImageUrls(item.content);
      const processedSolution = fixImageUrls(item.solution);

      console.log('🔗 图片URL修复完成');

      // 构建更好的HTML格式
      let text = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            font-size: 16px;
            background: white;
            color: black;
        }
        .material { margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .question { margin-bottom: 20px; font-weight: bold; }
        .options { margin: 10px 0; }
        .option { margin: 8px 0; padding: 5px; }
        .answer { margin-top: 20px; padding: 10px; background: #d4edda; border-left: 4px solid #28a745; }
    </style>
</head>
<body>

    <div class="material">${processedMaterial || ''}</div>
    <div class="question">${processedContent || ''}</div>
    <div class="options">
        <div class="option">A. ${item.answerone || ''}</div>
        <div class="option">B. ${item.answertwo || ''}</div>
        <div class="option">C. ${item.answerthree || ''}</div>
        <div class="option">D. ${item.answerfour || ''}</div>
    </div>
    <div class="answer">正确答案：${item.answer || ''}</div>
    <div class="solution">解析：${processedSolution || ''}</div>
</body>
</html>`;

      console.log('🖼️ 开始渲染题目为图片...');

      // 使用新的base64渲染方法
      let pic;
      try {
        pic = await ctx.service.ai.renderHtmlToImageBase64(text);

        // 检查渲染是否成功
        if (pic.status !== 200) {
          throw new Error(`图片渲染失败: ${pic.error || pic.message || '未知错误'}`);
        }
      } catch (renderError) {
        // 渲染错误也发送到飞书
        const renderErrorText = `❌ 图片渲染失败\n题目ID: ${item.id}\n时间: ${new Date().toLocaleString()}\n错误信息: ${renderError.message}\n错误堆栈: ${renderError.stack || '无堆栈信息'}`;
        try {
          await ctx.service.feishu['fs3'](renderErrorText);
        } catch (feishuError) {
          console.log('❌ 发送渲染错误信息到飞书失败:', feishuError.message);
        }
        throw renderError; // 重新抛出错误，让外层catch处理
      }

      console.log('✅ 图片渲染成功，开始AI文字提取...');

      // 显示截图信息
      if (pic.quality) {
        console.log(
          `📊 图片质量: ${pic.quality}%, 大小: ${(pic.fileSize / 1024 / 1024).toFixed(2)}MB`,
        );
        if (pic.filePath) {
          console.log(`📁 截图文件: ${pic.filePath}`);
        }
      }

      // extra是ai返回的答案，使用base64数据
      let extra;
      try {
        extra = await ctx.service.ai.extractTextFromImage(pic.base64, pic.mimeType);

        if (extra.status !== 200) {
          throw new Error(`AI文字提取失败: ${extra.error || extra.message || '未知错误'}`);
        }
      } catch (extractError) {
        // AI提取错误也发送到飞书
        const extractErrorText = `❌ AI文字提取失败\n题目ID: ${item.id}\n时间: ${new Date().toLocaleString()}\n错误信息: ${extractError.message}\n错误堆栈: ${extractError.stack || '无堆栈信息'}`;
        try {
          await ctx.service.feishu['fs3'](extractErrorText);
        } catch (feishuError) {
          console.log('❌ 发送AI提取错误信息到飞书失败:', feishuError.message);
        }
        throw extractError; // 重新抛出错误，让外层catch处理
      }

      console.log('✅ AI文字提取成功');
      console.log(`📝 提取内容长度: ${extra.text?.length || 0} 字符`);

      // 清理提取的文本，避免SQL注入
      const cleanedText = extra.text?.replace(/'/g, "''") || '';

      console.log('💾 开始更新数据库...');

      // 把答案更新到extra字段
      const updateSql = `UPDATE fbsy
                   SET extra = '${cleanedText}', ds = '重新更新'
                   WHERE parentid = ${item.parentid}`;
      try {
        await ctx.service.xr.query(updateSql);
      } catch (dbError) {
        // 数据库更新错误也发送到飞书
        const dbErrorText = `❌ 数据库更新失败\n题目ID: ${item.id}\nParentID: ${item.parentid}\n时间: ${new Date().toLocaleString()}\nSQL: ${updateSql}\n错误信息: ${dbError.message}\n错误堆栈: ${dbError.stack || '无堆栈信息'}`;
        try {
          await ctx.service.feishu['fs3'](dbErrorText);
        } catch (feishuError) {
          console.log('❌ 发送数据库错误信息到飞书失败:', feishuError.message);
        }
        throw dbError; // 重新抛出错误，让外层catch处理
      }

      console.log('✅ 数据库更新成功');
      let text1 = `https://vue.wcy9.com/fb/sy?biao=fbsy&type=sy&id=656604&ids=${item.id}\nhttp://127.0.0.1:6001/fb/sy?biao=fbsy&type=sy&id=656604&ids=${item.id}\n${dateNow()}\n${extra.text}`;
      await ctx.service.feishu['fs3'](text1);

      // 释放锁
      await app.redis.del(lockKey);
      console.log('🔓 处理完成，释放锁');

      // 输出详细的处理结果
      ctx.body = {
        success: true,
        message: '题目处理完成',
        data: {
          // 基本信息
          id: item.id,
          parentid: item.parentid,
          title: item.content?.substring(0, 50) + '...',

          // 处理结果
          extractedLength: extra.text?.length || 0,
          extractedPreview: extra.text?.substring(0, 200) + '...',

          // 题目详情
          hasContent: !!item.content,
          hasMaterial: !!item.material,
          hasOptions: !!(item.answerone && item.answertwo && item.answerthree && item.answerfour),
          answer: item.answer,

          // 处理状态
          renderStatus: pic.status,
          extractStatus: extra.status,
        },
      };
    } catch (error) {
      console.log('❌ 处理失败:', error.message);

      // 发送错误信息到飞书
      try {
        let errorContext = '';
        if (typeof item !== 'undefined') {
          errorContext = `\n题目ID: ${item.id || '未知'}\nParentID: ${item.parentid || '未知'}`;
        }

        const errorText = `❌ timutoextra处理失败${errorContext}\n时间: ${new Date().toLocaleString()}\n错误类型: ${error.name || 'Error'}\n错误信息: ${error.message}\n错误堆栈: ${error.stack || '无堆栈信息'}`;
        await ctx.service.feishu['fs3'](errorText);
      } catch (feishuError) {
        console.log('❌ 发送错误信息到飞书失败:', feishuError.message);
      }

      // 释放锁
      await app.redis.del(lockKey);
      console.log('🔓 处理失败，释放锁');

      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message,
        message: '题目处理失败',
      };
    }
  }

  /**
   * 图片识别提取文字方法
   * 使用GLM-4.1V-9B-Thinking模型进行图片文字提取
   */
  async extractTextFromImage() {
    const { ctx, app } = this;

    try {
      // 获取上传的文件
      const file = ctx.request.files && ctx.request.files[0];
      if (!file) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '请上传图片文件',
        };
        return;
      }

      // 读取文件并转换为base64
      const fs = require('fs');
      const sharp = require('sharp');
      let fileBuffer = fs.readFileSync(file.filepath);

      // 获取文件MIME类型
      const mimeType = file.mimeType || 'image/jpeg';

      // 检查文件大小，如果超过2MB则压缩
      const maxSize = 2 * 1024 * 1024; // 2MB
      const originalSize = fileBuffer.length;

      if (originalSize > maxSize) {
        app.logger.info(`📦 文件过大 (${(originalSize / 1024 / 1024).toFixed(2)} MB)，开始压缩...`);
        console.log(
          `📦 文件过大，正在压缩: ${(originalSize / 1024 / 1024).toFixed(2)} MB -> 目标: <2MB`,
        );

        try {
          // 使用sharp压缩图片
          let quality = 85; // 初始质量
          let compressedBuffer;

          do {
            compressedBuffer = await sharp(fileBuffer)
              .jpeg({ quality: quality, progressive: true })
              .toBuffer();

            if (compressedBuffer.length <= maxSize) {
              break;
            }

            quality -= 10; // 降低质量

            if (quality < 20) {
              // 如果质量太低，尝试调整尺寸
              const metadata = await sharp(fileBuffer).metadata();
              const scaleFactor = Math.sqrt(maxSize / compressedBuffer.length);
              const newWidth = Math.floor(metadata.width * scaleFactor);
              const newHeight = Math.floor(metadata.height * scaleFactor);

              compressedBuffer = await sharp(fileBuffer)
                .resize(newWidth, newHeight)
                .jpeg({ quality: 75, progressive: true })
                .toBuffer();
              break;
            }
          } while (compressedBuffer.length > maxSize);

          fileBuffer = compressedBuffer;
          const compressedSize = fileBuffer.length;
          const compressionRatio = (((originalSize - compressedSize) / originalSize) * 100).toFixed(
            1,
          );

          app.logger.info(
            `✅ 压缩完成: ${(originalSize / 1024 / 1024).toFixed(2)} MB -> ${(compressedSize / 1024 / 1024).toFixed(2)} MB (压缩率: ${compressionRatio}%)`,
          );
          console.log(
            `✅ 压缩完成: ${(compressedSize / 1024 / 1024).toFixed(2)} MB (压缩率: ${compressionRatio}%)`,
          );
        } catch (compressionError) {
          app.logger.error('❌ 图片压缩失败:', compressionError);
          console.log('❌ 图片压缩失败，使用原图片');
          // 如果压缩失败，检查原图是否太大
          if (originalSize > maxSize * 2) {
            ctx.status = 400;
            ctx.body = {
              success: false,
              message: `图片文件过大 (${(originalSize / 1024 / 1024).toFixed(2)} MB)，且压缩失败。请上传小于4MB的图片。`,
            };
            return;
          }
        }
      } else {
        app.logger.info(`✅ 文件大小合适: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);
      }

      const base64Image = fileBuffer.toString('base64');

      app.logger.info('📸 开始处理图片文字提取请求');
      app.logger.info(
        `📊 文件信息: ${file.filename} (${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB, ${mimeType})`,
      );

      // 控制台输出开始信息
      console.log('\n' + '='.repeat(60));
      console.log(`🖼️  开始处理图片: ${file.filename}`);
      if (originalSize !== fileBuffer.length) {
        console.log(`📏 原始大小: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`📏 压缩后大小: ${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB`);
      } else {
        console.log(`📏 文件大小: ${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB`);
      }
      console.log(`🎨 文件类型: ${mimeType}`);
      console.log('='.repeat(60));

      // 调用service中的非流式方法
      const result = await ctx.service.ai.extractTextFromImage(base64Image, mimeType);

      // 提取响应内容（保持兼容性）
      const extractedText = result.text;

      app.logger.info('图片文字提取成功');
      app.logger.info(`提取内容长度: ${extractedText?.length || 0} 字符`);

      // 清理临时文件
      try {
        fs.unlinkSync(file.filepath);
        app.logger.info('🗑️ 临时文件已清理');
      } catch (cleanupError) {
        app.logger.warn('⚠️ 清理临时文件失败:', cleanupError);
      }

      // 控制台输出结果摘要
      console.log('='.repeat(60));
      console.log(`📝 提取完成! 内容长度: ${extractedText?.length || 0} 字符`);
      console.log('='.repeat(60) + '\n');

      // 返回结果
      if (result.status === 200) {
        ctx.body = {
          success: true,
          message: result.message,
          data: {
            extractedText: extractedText,
            fileInfo: {
              originalName: file.filename,
              size: fileBuffer.length,
              mimeType: mimeType,
            },
          },
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          message: result.message,
          error: result.error,
        };
      }
    } catch (error) {
      app.logger.error('❌ 图片文字提取失败:', error);

      // 控制台输出错误信息
      console.log('\n' + '='.repeat(60));
      console.log('❌ 图片处理失败!');
      console.log(`🚨 错误信息: ${error.message}`);
      console.log('='.repeat(60) + '\n');

      // 清理临时文件（如果存在）
      const file = ctx.request.files && ctx.request.files[0];
      if (file && file.filepath) {
        try {
          const fs = require('fs');
          fs.unlinkSync(file.filepath);
        } catch (cleanupError) {
          // 忽略清理错误
        }
      }

      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '图片文字提取失败',
        error: error.message,
      };
    }
  }

  // 查看锁状态的方法
  async checkLock() {
    const { ctx, app } = this;
    const lockKey = 'timutoextra_processing_lock';
    const isLocked = await app.redis.get(lockKey);
    const ttl = await app.redis.ttl(lockKey);

    ctx.body = {
      success: true,
      data: {
        isLocked: !!isLocked,
        lockValue: isLocked,
        ttl: ttl, // 剩余时间（秒）
        message: isLocked ? `锁定中，剩余时间: ${ttl}秒` : '未锁定',
      },
    };
  }

  // 手动释放锁的方法（紧急情况使用）
  async releaseLock() {
    const { ctx, app } = this;
    const lockKey = 'timutoextra_processing_lock';
    const result = await app.redis.del(lockKey);

    ctx.body = {
      success: true,
      message: result ? '锁已释放' : '锁不存在或已释放',
      released: !!result,
    };
  }

  async thinkprocess() {
    const { ctx, app } = this;
    let body = ctx.request.body;
    let content = body.content;

    // 打印客户端连接信息
    console.log('=== thinkprocess 接口被调用 ===');
    console.log('客户端IP:', ctx.ip);
    console.log('请求方法:', ctx.method);
    console.log('请求URL:', ctx.url);
    console.log('请求头:', ctx.headers);
    console.log('请求体:', body);
    console.log('时间:', new Date().toLocaleString());
    console.log('==============================');

    try {
      // 从Redis获取最新的思考过程
      const thinkingData = await app.redis.get('thinking_process');
      const answerData = await app.redis.get('thinking_answer');

      console.log('Redis数据获取结果:');
      console.log('- thinkingData:', thinkingData ? '有数据' : '无数据');
      console.log('- answerData:', answerData ? '有数据' : '无数据');

      if (thinkingData) {
        const thinking = JSON.parse(thinkingData);
        console.log('✅ 成功返回思考数据');
        ctx.body = {
          success: true,
          thinking: thinking,
          answer: answerData ? JSON.parse(answerData) : null,
        };
      } else {
        console.log('⚠️ 暂无思考数据');
        ctx.body = {
          success: false,
          message: '暂无思考数据',
        };
      }
    } catch (error) {
      console.error('❌ 获取思考过程失败:', error);
      ctx.body = {
        success: false,
        message: '获取思考过程失败',
        error: error.message,
      };
    }

    console.log('=== thinkprocess 接口调用结束 ===\n');
  }

  async sili() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    ctx.body = await ctx.service.ai.sili(content);
  }

  async save() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    let data = await ctx.service.xr['query'](`SELECT *
                                              FROM fbsy
                                              WHERE allcateid like '%656604%'
                                                and (content like '%.cn%')
                                              ORDER BY id desc limit 1`);
    let text = `
  \n${data[0].material}\n
  ====================================
`;
    text = text
      .replace(/fenbike\.cn/g, 'fbstatic.cn')
      .replace(/src=(['"])\/\/([^'"]+)/g, 'src=$1https://$2');
    // console.log(text);
    ctx.body = await ctx.service.ai['renderHtmlToImage'](text);
  }

  async save1() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    // let data = await ctx.service.xr['query'](`SELECT *
    //                                        FROM fbsy
    //                                        WHERE (content like '%http%' or solution like '%http%')
    //                                        ORDER BY id desc limit 1`);
    // let savepath = await ctx.service.ai.renderHtmlToImage(content);
    // console.log(savepath);
    ctx.body = await ctx.service.ai.silipic(
      `你能看到这张图吗`,
      'C:/Users/<USER>/Desktop/question-2025-06-05.png',
    );
  }

  async updatefbsyzc() {
    const { ctx, app, logger } = this;
    let per = ctx['query'].per;
    let type = ctx['query'].type || 0;
    let type1 = ctx['query'].type1 || false;
    let model = ctx['query'].model;
    let up = ctx['query'].up;
    let lock = ctx['query'].lock || false;
    let ss = ctx['query'].ss || false;
    let whatmodel = 1;
    if (lock) {
      await ctx.service.feishu['fs3']('【updatefbsyzc】lock分支，重置相关redis和ds');
      const keys = await app.redis.keys('zhihuwenda*');
      for (const key of keys) {
        await app.redis.set(key, 0);
      }
      await app.redis.set('sili', 0);
      await app.redis.set(`sili:count`, 0);
      await ctx.service.xr['query'](`UPDATE fbsy SET ds = NULL WHERE ds = 'ds'`);
      ctx.body = await app.redis.get('zhihuwenda');
      return;
    }
    if (ss) {
      await ctx.service.feishu['fs3']('【updatefbsyzc】ss分支，重置部分ds');
      await ctx.service.xr['query'](
        `UPDATE fbsy SET ds = NULL WHERE allcateid like '%656600%' and ds = 'ds'`,
      );
      await app.redis.set('zhihuwenda2', 0);
      ctx.body = await app.redis.get('zhihuwenda2');
      return;
    }
    const redisLockMap = {
      2: { key: 'sili', message: '正在处理其他问题，请稍后再试' },
      3: { key: 'zhihuwenda', message: '正在处理其他问题，请稍后再试' },
      4: { key: 'zhihuwenda1', message: '正在处理其他问题，请稍后再试' },
      5: { key: 'zhihuwenda2', message: '正在处理其他问题，请稍后再试' },
      6: { key: 'zhihuwenda2', message: '正在处理其他问题，请稍后再试2' },
      7: { key: 'zhihuwenda3', message: '正在处理其他问题，请稍后再试2' },
      8: { key: 'zhihuwenda4', message: '正在处理其他问题，请稍后再试2' },
      9: { key: 'zhihuwenda5', message: '正在处理其他问题，请稍后再试2' },
      11: { key: 'zhihuwenda6', message: '正在处理其他问题，请稍后再试2' },
      12: { key: 'zhihuwenda7', message: '正在处理其他问题，请稍后再试2' },
    };
    const lockConfig = redisLockMap[+model];
    if (lockConfig) {
      const isLocked = await app.redis.get(lockConfig.key);
      if (+isLocked === 1) {
        ctx.body = {
          success: false,
          answers: lockConfig.message,
        };
        return;
      }
    }
    const startTime = Date.now();
    let option = `allcateid like '%${type}%'`;
    if (type1) {
      option = `(allcateid like '%${type}%' or allcateid like '%${type1}%')`;
    }
    if (+model === 10) {
      option = ` (allcateid like '%656618%' or allcateid like '%656610%' or allcateid like '%656608%' or allcateid like '%656704%')`;
    }
    let sql = `SELECT * FROM fbsy WHERE ${option} AND (ds IS NULL OR (ds NOT LIKE '%手稿%' AND ds NOT LIKE '%无法回答%' AND ds NOT LIKE '%无法针对%' AND ds != 'ds')) ORDER BY id DESC LIMIT ${per}`;
    if (up) {
      sql = `SELECT * FROM fbsy WHERE (ds like '%无法回答%' or ds like '%无法针对%') ORDER BY id desc limit ${per}`;
    }
    let res = await ctx.service.xr['query'](sql);
    let res1 = await ctx.service.xr['query'](
      `SELECT * FROM fbsy WHERE (ds = '重新更新') ORDER BY id desc limit ${per}`,
    );
    if (!res || res.length === 0) {
      ctx.body = {
        message: '零',
      };
      return;
    }
    if (!up) {
      res = res1;
    }
    let list = [];
    try {
      let timu = '';
      for (let item of res) {
        await ctx.service.feishu['fs3']('【updatefbsyzc】处理item，id=' + item.id);
        timu = item.content;

        const isds = item?.ds ? item?.ds : '';
        // 构建最终的文本
        let text = `
         ${qiaopi}\n
  ${item?.material ? item.material : ''}\n${item.content}\n
  ===========================
   \n${item?.extra ? '这部分是电脑识别的ocr数据，数据可能不准,主要看解析' + item?.extra : ''}\n
  ==========================
  \nA.${item.answerone}\n
  \nB.${item.answertwo}\n
  \nC.${item.answerthree}\n
  \nD.${item.answerfour}\n
  ====================================
  \n答案是${item.answer}\n
  \n ${
    !(item.allcateid?.split(',') || []).some((id) => ['656604'].includes(id.trim()))
      ? `\n解析参考（主要要以你深度思考为主，解析是只是参考）：${item.solution}\n`
      : ''
  }
\n
  一定要多用换行(狂用mathjax的渲染。多点表格进行对比，Markdown 渲染器要求表格前后必须有空行，否则无法正确解析。)，多用emoji丰富回答哟！
 `;
        text = text.replace(/fenbike\.cn/g, 'fbstatic.cn').replace(/\/\/fb\./g, 'https://fb.');

        const now = dayjs().tz('Asia/Shanghai');
        const hour = now.hour();
        const minute = now.minute();
        const inRange =
          (hour === 0 && minute >= 30) || // 00:30 ~ 00:59
          (hour > 0 && hour < 8) || // 01:00 ~ 07:59
          (hour === 8 && minute <= 30); // 08:00 ~ 08:30
        timu = text;
        let ai;
        const modelMap = {
          2: { key: 'sili', serviceMethod: 'tuili' },
          3: {
            key: 'zhihuwenda',
            serviceMethod: 'askQuestion3',
            token: 'hf86Hvg9ehsOY0yLuTZc6xGu2bbn7fF9',
            xone: '2.0_tKrURCmBSP3=icg07Cq7TPGcSfcFmRCDgzovjvmzD2g2I7bydz3xWMK=evX42bxh',
            xtwo: '2.0_ReftDnS0cXK9v/tiseVNggO4jUCY91RuF4Z6=t+uamIIbdkLRZifOU0SdBGxMJAs',
            session_id: '3673569108947248099',
            cookie: 'zhcookie',
          },
          4: {
            key: 'zhihuwenda1',
            serviceMethod: 'askQuestion3',
            token: 'hf86Hvg9ehsOY0yLuTZc6xGu2bbn7fF9',
            xone: '2.0_tKrURCmBSP3=icg07Cq7TPGcSfcFmRCDgzovjvmzD2g2I7bydz3xWMK=evX42bxh',
            xtwo: '2.0_ReftDnS0cXK9v/tiseVNggO4jUCY91RuF4Z6=t+uamIIbdkLRZifOU0SdBGxMJAs',
            session_id: '3673569108947248099',
            cookie: 'zhcookie',
          },
        };

        const config = modelMap[+model];
        let timeout;
        try {
          if (config) {
            const { key, serviceMethod, token, xone, xtwo, session_id, cookie } = config;
            const isLocked = await app.redis.get(key);
            if ((+isLocked === 3 || +isLocked === 1) && key !== 'sili:count') {
              ctx.body = {
                success: false,
                answers: '正在处理其他问题，请稍后再试',
              };
              return;
            }
            whatmodel = +model !== 2 ? '知乎问答' : '硅基流动收尾';
            +model !== 10 ? await app.redis.set(key, '1') : '';

            await ctx.service.xr['update']('fbsy', {
              id: item.id,
              ds: 'ds',
              choice: +model,
            });
            if (text.match('.cn/') && false) {
              let x;
              while (true) {
                try {
                  x = await ctx.service.ai['renderHtmlToImage'](text);
                  // 调用成功，跳出循环
                  break;
                } catch (err) {
                  console.error('renderHtmlToImage 失败，正在重试...', err);
                  // 这里可以加一个短延迟，避免过于频繁地打接口
                  await new Promise((r) => setTimeout(r, 500));
                }
              }
              timu = x?.text ?? '';
              text = timu + `\n===================================\n` + text;
            }
            ai = await ctx.service.ai[serviceMethod](
              text,
              token,
              xone,
              xtwo,
              session_id,
              cookie,
              model,
              key,
            );
          }
        } finally {
          // 记录结束时间
          const endTime = Date.now();

          // 计算耗时（毫秒）
          const elapsed = endTime - startTime;
          // 输出耗时
          const minutes = Math.floor(elapsed / 60000);
          const seconds = ((elapsed % 60000) / 1000).toFixed(2);
          // if (timeout) {
          //   logger.info(`定时器 (ID: ${timeout}) 已清除`);
          // } else {
          //   logger.info('没有定时器需要清除');
          // }

          logger.info(`updatefbsyzc执行耗时: ${minutes} 分 ${seconds} 秒`);
          if (+minutes === 0 && seconds < 6) {
            logger.error('耗时小于6秒', ai?.status || 401, item.id, item.content);
            await ctx.service.xr['query'](`UPDATE fbsy
                                           SET ds  = '无法回答',
                                               tag = ${+item.tag + 1}
                                           WHERE id = ${item.id}`);
          }

          list.push(ai);
          try {
            if (ai?.status === 200) {
              let w = await ctx.service.xr['update']('fbsy', {
                id: item.id,
                ds: ai.data.replace(/```/g, ''),
                choice: null,
              });
            } else if (+model !== 10) {
              logger.error('ai状态不是200,model不是10', ai?.status || 401, item.id, item.content);
              await ctx.service.xr['query'](`UPDATE fbsy
                                             SET ds  = null,
                                                 tag = ${+item.tag + 1}
                                             WHERE id = ${item.id}`);
            } else if (+model === 10) {
              logger.error('ai状态不是200，model是10', ai?.status || 401, item.id, item.content);
              await ctx.service.xr['query'](`UPDATE fbsy
                                             SET ds = '无法回答'
                                             WHERE id = ${item.id}`);
            } else {
              logger.error('进入你好了', ai?.status || 402, item.id, item.content);
              ctx.service.feishu['fs']('进入你好了:' + item.id);
              await ctx.service.xr['query'](`UPDATE fbsy
                                             SET ds = '你好'
                                             WHERE id = ${item.id}`);
            }
          } catch (e) {
            const text = e?.response?.data?.error?.message || e?.message || '未知错误';
            ctx.service.feishu['fs']('更新出现了' + text);
          } finally {
          }

          if (item?.ds?.includes?.('延迟退休是基于我国经济')) {
            let s = `update fbsy
                     set ds = '无法回答'
                     where id = ${item.id}`;
            await ctx.service.xr['query'](s);
          }
          if (+item.tag > 3) {
            logger.error('item.tag超过3了', item.id, item.content);
            await ctx.service.feishu['fs'](`item.tag超过3了,${item.id},${item.content}`);
            await ctx.service.xr['query'](`UPDATE fbsy
                                           SET ds  = '无法回答',
                                               tag = ${+item.tag + 1}
                                           WHERE id = ${item.id}`);
          }

          if (+item.tag > 10) {
            logger.error('item.tag超过10了', item.id, item.content);
            await ctx.service.feishu['fs'](`item.tag超过10了,${item.id},${item.content}`);
            await ctx.service.xr['query'](`UPDATE fbsy
                                           SET ds = '手稿'
                                           WHERE id = ${item.id}`);
          }

          let kong = await ctx.service.xr['query'](`SELECT count(id) as kong
                                                    FROM fbsy
                                                    WHERE ${option}
                                                      AND (
                                                      ds IS NULL
                                                        OR (
                                                        ds NOT LIKE '%手稿%'
                                                          AND ds NOT LIKE '%无法回答%'
                                                          AND ds NOT LIKE '%无法针对%'
                                                          AND ds != 'ds'
                                                        )
                                                      )
                                                    ORDER BY id desc`);

          let bukong = await ctx.service.xr['query'](`SELECT count(id) as bukong
                                                      FROM fbsy
                                                      where ${option}
                                                        and ds LIKE '%手稿%'
                                                      ORDER BY id desc`);

          //item.allcateid->'656602,796962,796971,797321'
          async function getCateNameById(id) {
            const result = await ctx.service.xr['query'](`SELECT name
                                                          FROM fbsycate
                                                          WHERE id = ${id}`);
            return result[0]?.name || '';
          }

          const cateid = item.allcateid.split(',')[0];
          const catename = await getCateNameById(cateid);

          let typenum = '';
          if (item.allcateid.includes(type)) {
            typenum = await getCateNameById(type);
          } else {
            typenum = cateid;
          }
          let text1 = `${typenum}剩余${kong[0].kong}，已完成${bukong[0].bukong}\nper:${per}，type:${type}，id:${item.id}，model:${+model}\nhttps://vue.wcy9.com/fb/sy?biao=fbsy&type=sy&id=${type}&ids=${item.id}\nhttp://127.0.0.1:6001/fb/sy?biao=fbsy&type=sy&id=${type}&ids=${item.id}\n现在的model是${whatmodel}\n执行耗时: ${minutes} 分 ${seconds} 秒\n${dateNow()}\n${timu}`;
          await ctx.service.feishu['fs3'](text1);
        }
      }
    } finally {
      const redisSetMap = {
        2: 'sili',
        3: 'zhihuwenda',
        4: 'zhihuwenda1',
        5: 'zhihuwenda2',
        6: 'zhihuwenda2',
        7: 'zhihuwenda3',
        8: 'zhihuwenda4',
        9: 'zhihuwenda5',
        11: 'zhihuwenda6',
        12: 'zhihuwenda7',
      };
      const redisKey = redisSetMap[+model];
      const isLocked = await app.redis.get(redisKey);
      if (redisKey) {
        if (+isLocked !== 3 || +isLocked === 1) {
          await app.redis.set(redisKey, '2');
          logger.info('更新redis成功', isLocked);
        }
      } else {
        logger.error('没更新redis成功', isLocked);
        console.log(+model); // 处理未定义的 model 类型
      }
      ctx.body = list;
    }
  }

  /**
   * BullMQ版本的updatefbsyzc
   * 使用队列处理，无需手动锁管理
   */
  async update_renew() {
    const { ctx, app, logger } = this;
    const startTime = Date.now();

    try {
      // 获取参数
      let per = ctx.query.per || 1;
      let type = ctx.query.type || 0;
      let type1 = ctx.query.type1 || false;
      let model = ctx.query.model;
      let up = ctx.query.up;
      let lock = ctx.query.lock || false;
      let ss = ctx.query.ss || false;

      // 处理特殊参数
      if (lock) {
        await ctx.service.feishu.fs3('【update_renew】lock分支，重置相关redis和ds');
        const keys = await app.redis.keys('zhihuwenda*');
        for (const key of keys) {
          await app.redis.set(key, 0);
        }
        await app.redis.set('sili', 0);
        await app.redis.set('sili:count', 0);
        await ctx.service.xr.query(`UPDATE fbsy SET ds = NULL WHERE ds = 'ds'`);
        ctx.body = await app.redis.get('zhihuwenda');
        return;
      }

      if (ss) {
        await ctx.service.feishu.fs3('【update_renew】ss分支，重置部分ds');
        await ctx.service.xr.query(
          `UPDATE fbsy SET ds = NULL WHERE allcateid like '%656600%' and ds = 'ds'`,
        );
        await app.redis.set('zhihuwenda2', 0);
        ctx.body = await app.redis.get('zhihuwenda2');
        return;
      }

      // 验证模型参数
      if (!model) {
        ctx.body = {
          success: false,
          message: '缺少model参数',
        };
        return;
      }

      // 获取队列配置
      const { getBullMQConfig, getQueueConfigByModel } = require('../../config/bullmq');
      const queueConfig = getQueueConfigByModel(+model);

      if (!queueConfig) {
        ctx.body = {
          success: false,
          message: `不支持的模型类型: ${model}`,
        };
        return;
      }

      // 添加作业到队列
      const { Queue } = require('bullmq');
      const bullmqConfig = getBullMQConfig(app.config.env);
      const queue = new Queue(queueConfig.name, { connection: bullmqConfig.connection });

      const jobData = {
        per: +per,
        type,
        type1,
        model: +model,
        up: !!up,
        timestamp: Date.now(),
        source: 'update_renew',
      };

      const job = await queue.add('process-fbsy', jobData, {
        attempts: queueConfig.attempts,
        backoff: queueConfig.backoff,
        removeOnComplete: queueConfig.removeOnComplete,
        removeOnFail: queueConfig.removeOnFail,
      });

      const elapsed = Date.now() - startTime;
      logger.info(`update_renew添加作业耗时: ${elapsed}ms, jobId: ${job.id}`);

      await ctx.service.feishu.fs3(
        `【update_renew】作业已添加到队列 ${queueConfig.name}, jobId: ${job.id}, model: ${model}`,
      );

      ctx.body = {
        success: true,
        message: '作业已添加到队列',
        jobId: job.id,
        queueName: queueConfig.name,
        model,
        data: jobData,
      };
    } catch (error) {
      logger.error('update_renew错误:', error);
      await ctx.service.feishu.fs(`update_renew错误: ${error.message}`);

      ctx.body = {
        success: false,
        message: error.message,
        error: error.stack,
      };
    }
  }

  /**
   * 切换BullMQ模式
   * 动态控制是否使用BullMQ处理任务
   */
  async toggleBullMQ() {
    const { ctx, app, logger } = this;

    try {
      const { enable } = ctx.query;

      if (enable === undefined) {
        // 获取当前状态
        const currentStatus = app.config.server?.features?.useBullMQ || false;
        ctx.body = {
          success: true,
          useBullMQ: currentStatus,
          message: `当前${currentStatus ? '启用' : '禁用'}BullMQ模式`,
        };
        return;
      }

      // 设置新状态
      const newStatus = enable === 'true' || enable === '1';

      // 动态更新配置
      if (!app.config.server) {
        app.config.server = { features: {} };
      }
      if (!app.config.server.features) {
        app.config.server.features = {};
      }

      app.config.server.features.useBullMQ = newStatus;

      logger.info(`BullMQ模式已${newStatus ? '启用' : '禁用'}`);

      await ctx.service.feishu.fs3(`【配置变更】BullMQ模式已${newStatus ? '启用' : '禁用'}`);

      ctx.body = {
        success: true,
        useBullMQ: newStatus,
        message: `BullMQ模式已${newStatus ? '启用' : '禁用'}`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('切换BullMQ模式失败:', error);
      ctx.body = {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * 获取BullMQ状态信息
   */
  async bullmqStatus() {
    const { ctx, app, logger } = this;

    try {
      const useBullMQ = app.config.server?.features?.useBullMQ || false;
      const bullmqEnabled = app.config.bullmq?.enable !== false;

      let workersStatus = {};
      if (app.bullmqWorkers) {
        workersStatus = app.bullmqWorkers.getStatus();
      }

      ctx.body = {
        success: true,
        config: {
          useBullMQ,
          bullmqEnabled,
        },
        workers: workersStatus,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('获取BullMQ状态失败:', error);
      ctx.body = {
        success: false,
        message: error.message,
      };
    }
  }

  async dstotal() {
    const { ctx, app } = this;
    let dstotal = await ctx.service.xr['query'](`SELECT count(*) as total
                                                 FROM fbsy
                                                 where ds = 'ds'`);
    let total = dstotal[0].total;
    await ctx.service.feishu['fs3'](`ds存在${total}条`);
    ctx.body = dstotal;
  }

  async setzhcookie() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    await app.redis.set('zhcookie', cookies.cookie);
    ctx.body = await app.redis.get('zhcookie');
  }

  async setzhcookie1() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    await app.redis.set('zhcookie1', cookies.cookie);
    ctx.body = await app.redis.get('zhcookie1');
  }

  async setzhcookie2() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    await app.redis.set('zhcookie2', cookies.cookie);
    ctx.body = await app.redis.get('zhcookie2');
  }

  async setzhcookie3() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    await app.redis.set('zhcookie3', cookies.cookie);
    ctx.body = await app.redis.get('zhcookie3');
  }

  async doubao() {
    const { ctx, service } = this;
    const { prompt } = ctx.request.body;

    if (!prompt) {
      ctx.body = { status: 400, message: '缺少 prompt 参数' };
      return;
    }

    const result = await service.ai.hunyuan(prompt);
    ctx.body = result;
  }

  async ask() {
    const { ctx, service, app } = this;
    const { question } = ctx.request.body;

    console.log(question);
    if (!question) {
      ctx.throw(400, '问题不能为空');
    }

    let isLocked = await app.redis.get('zhihuwenda');
    if (+isLocked === 1) {
      ctx.body = {
        success: false,
        answers: '正在处理其他问题，请稍后再试',
      };
      throw new Error('正在处理其他问题，请稍后再试');
    }
    const result = await service.ai.askQuestion(question);
    ctx.body = {
      success: true,
      answers: result,
    };
  }

  async silistream() {
    const { ctx, service, app } = this;
    // const content = ctx['query'].content || '你好';
    const status = await app.redis.get('sili');
    const reasoning = (await app.redis.get('sili:reasoning')) || '';
    const final = (await app.redis.get('sili:final')) || '';

    ctx.body = {
      status,
      reasoning,
      final,
    };
    return;
    ctx.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
    });

    ctx.status = 200;
    ctx.res.write(': connected\n\n');

    await service.ai.streamSili(content, (chunk) => {
      ctx.res.write(chunk);
    });

    ctx.res.end();
    console.log(1);
  }

  async sw() {
    const { ctx } = this;
    let sw = ctx['query'].sw;
    const res = await ctx.service.xr['update'](
      'sw',
      {
        id: 4,
        sw: +sw,
      },
      {
        where: {
          name: 'ai',
        },
      },
    );
    // console.log(res);
    ctx.body = await ctx.service.xr.find('sw', { name: 'ai' });
  }

  async silistatus() {
    const { ctx, app } = this;

    ctx.body = {
      name: this.config.vps,
    };
    // 注意：不再是 SSE，用普通 JSON 返回就行了
  }

  async setnull() {
    const { ctx, app } = this;
    let { id, type, biao } = ctx['query'];

    const res = await app.mysql['update'](biao, {
      id: id,
      ds: '重新更新',
    });
    // console.log(res);
    ctx.body = await ctx.service.xr['find'](biao, { id: id });
  }

  async updatekj() {
    const { ctx, app, logger } = this;
    let { biao, model } = ctx['query'];
    biao = biao ? biao : 'hsljpd';
    let allcateid = '1';
    const startTime = Date.now();
    const redisLockMap = {
      2: { key: 'sili', message: '正在处理其他问题，请稍后再试' },
      3: { key: 'zhihuwenda', message: '正在处理其他问题，请稍后再试' },
      4: { key: 'zhihuwenda1', message: '正在处理其他问题，请稍后再试' },
      5: { key: 'zhihuwenda2', message: '正在处理其他问题，请稍后再试' },
      6: { key: 'zhihuwenda2', message: '正在处理其他问题，请稍后再试2' },
      7: { key: 'zhihuwenda3', message: '正在处理其他问题，请稍后再试2' },
      8: { key: 'zhihuwenda4', message: '正在处理其他问题，请稍后再试2' },
      9: { key: 'zhihuwenda5', message: '正在处理其他问题，请稍后再试2' },
      11: { key: 'zhihuwenda6', message: '正在处理其他问题，请稍后再试2' },
      12: { key: 'zhihuwenda7', message: '正在处理其他问题，请稍后再试2' },
    };

    const lockConfig = redisLockMap[+model];

    if (lockConfig) {
      const isLocked = await app.redis.get(lockConfig.key);
      if (+isLocked === 1) {
        ctx.body = {
          success: false,
          answers: lockConfig.message,
        };
        return;
      }
    }
    let data = await ctx.service.xr['query'](`SELECT *
                                              FROM ${biao}
                                              where (ds is null or (ds = '重新更新')) limit 1`);
    let whatmodel, ai, timu;
    let option = '1=1';
    let item = data[0];
    // logger.info('item.id', biao, item);
    if (!item) {
      return;
    }
    allcateid = item?.allcateid;
    let text = `
  ${item.content}\n
  \nA.${item.answerone}\n
  \nB.${item.answertwo}\n
  \nC.${item.answerthree}\n
  \nD.${item.answerfour}\n
  ====================================
  \n${item.solution}\n
  \n${qiaopi}`;
    text = text.replace(/fenbike\.cn/g, 'fbstatic.cn').replace(/\/\/fb\./g, 'https://fb.');
    if (1 > 2) {
      await ctx.service.xr['update'](biao, {
        id: item.id,
        ds: '手稿',
      });
    } else {
      const modelMap = {
        2: { key: 'sili', serviceMethod: 'index' },
        3: {
          key: 'zhihuwenda',
          serviceMethod: 'askQuestion3',
          token: 'hf86Hvg9ehsOY0yLuTZc6xGu2bbn7fF9',
          xone: '2.0_tKrURCmBSP3=icg07Cq7TPGcSfcFmRCDgzovjvmzD2g2I7bydz3xWMK=evX42bxh',
          xtwo: '2.0_ReftDnS0cXK9v/tiseVNggO4jUCY91RuF4Z6=t+uamIIbdkLRZifOU0SdBGxMJAs',
          session_id: '3673569108947248099',
          cookie: 'zhcookie',
        },
        // 4: {
        //   key: 'zhihuwenda1',
        //   serviceMethod: 'askQuestion3',
        //   token: 'dM1dAcakqPLEaOE2YHyKw3Ggwfsukiyb',
        //   xone: '2.0_aCj8vM+yN5liZ=0jmFQnDKdJl3URKlxwDJVAKapkeLie12Nt87Y3fNORx1MdamRy',
        //   xtwo: '2.0_eHgboZq058eNinU2z6aaHjH9ACc606geI30Iq7mPmYY2z9J0kZbPY1rfVxzGygQD',
        //   session_id: '3668887385997162569',
        //   cookie: 'zhcookie',
        // },
        // 5: {
        //   key: 'zhihuwenda2',
        //   serviceMethod: 'askQuestion3',
        //   token: 'TgS1yBNCSDo17ihiREO4YN7IXp8u9dQf',
        //   xone: '2.0_S7NhD7tkP7Kf26nkpkNspbMAblS9Sv/nzqTkJMs930Dp6nro97y/gSktskLorvY0',
        //   xtwo: '2.0_+Tatb7gMA/CZZO2sz3sB+36j5kjAMtLkNDVGB2NUu/V58BnGvyfKU42u9n4LDYJU',
        //   session_id: '3668505552211488450',
        //   cookie: 'zhcookie1',
        // },
        // 7: {
        //   key: 'zhihuwenda3',
        //   serviceMethod: 'askQuestion3',
        //   token: 'TgS1yBNCSDo17ihiREO4YN7IXp8u9dQf',
        //   xone: '2.0_hFhfXM=UB=zXyQ/Q/bR0CZK=PlJn7DofCRzKhJ4dgIPu5QtzoDe+/OPlxRuB3Yiz',
        //   xtwo: '2.0_CThWAe6XtckFO8NRT1la3zzR2bybQDmBguGbKoci5aXL66ZV1gl2YBkee46nnn6G',
        //   session_id: '3667964787493984256',
        //   cookie: 'zhcookie1',
        // },
        // 8: {
        //   key: 'zhihuwenda4',
        //   serviceMethod: 'askQuestion3',
        //
        //   token: 'c0235a19-1cc7-4774-bdd2-7cd1d5a3390a',
        //   xone: '2.0_m=xePqnO3tcnVgVnSIWWwmuNRiT8iitzWmQG7rRW0BttWXpneRnyT3I7DqDwbLzI',
        //   xtwo: '2.0_nAqKqqSXHwCISPQgyDL/HPZ=S84HId122lw5XV3CG6Ij2YSu68ftj7UqdbSqsIdZ',
        //   session_id: '3668007421848194238',
        //   cookie: 'zhcookie2',
        // },
        // 9: {
        //   key: 'zhihuwenda5',
        //   serviceMethod: 'askQuestion3',
        //   token: 'c0235a19-1cc7-4774-bdd2-7cd1d5a3390a',
        //   xone: '2.0_Q7d+Mcqnuf0xRgvLd5SX6mFiJZ0gDFuCByO4VJF5/MMzWzDPqYc8yt=RnJByCWgV',
        //   xtwo: '2.0_3joqxdiv25xn2RnWras4hrzX261HsuBT=UftUsZUubxgzPO=BTGtzFjZkrtLaBgu',
        //   session_id: '3668513121609117329',
        //   cookie: 'zhcookie2',
        // },
        // 11: {
        //   key: 'zhihuwenda6',
        //   serviceMethod: 'askQuestion3',
        //   token: 'e7876740-96d9-4bdf-9ffc-1070e87013cb',
        //   xone: '2.0_HclU8rPN+SC4GAuW08uvHeSZTVC7tQFv+yvNxaS5cFRrRs3WnINlMJHhknsPbrrc',
        //   xtwo: '2.0_RCYZsZU3kwY0fdY3dwvkGzZByJNLOymMtON8YjwAYbUmCkXXoMlqZhqVBYHj3Dec',
        //   session_id: '3668917256039674200',
        //   cookie: 'zhcookie3',
        // },
        // 12: {
        //   key: 'zhihuwenda7',
        //   serviceMethod: 'askQuestion3',
        //   token: 'e7876740-96d9-4bdf-9ffc-1070e87013cb',
        //   xone: '2.0_9bpX6V0Q8veDTiwQ1IhNsRXHHyN0mo/p218lQkFstLcY=K0KKfyJ9J0NBDeaKcJF',
        //   xtwo: '2.0_fAJl1PCJ=oYD92GcgL=GUpsqCq1CqD0kffxnvAF2llJrqCaB65wqPYJ/2x/IaO9O',
        //   session_id: '3668917730937654223',
        //   cookie: 'zhcookie3',
        // },
      };

      const config = modelMap[+model];
      try {
        if (config) {
          const { key, serviceMethod, token, xone, xtwo, session_id, cookie } = config;
          const isLocked = await app.redis.get(key);
          if ((+isLocked === 3 || +isLocked === 1) && key !== 'sili:count') {
            ctx.body = {
              success: false,
              answers: '正在处理其他问题，请稍后再试',
            };
            return;
          }
          whatmodel = +model !== 2 ? '知乎问答' : '硅基流动收尾';
          +model !== 10 ? await app.redis.set(key, '1') : '';
          //&&
          //             +item.id % 130 >= 61 &&
          //             +item.id % 130 <= 70
          if (item.content.match('.cn/') && false) {
            let x;
            let qs = `
  ${item.content}\n<br>
  ====================================<br>
  \nA.${item.A}\n<br>
  \nB.${item.B}\n<br>
  \nC.${item.C}\n<br>
  \nD.${item.D}\n<br>
  ====================================
  \n${item.solution}\n<br>
`;
            await ctx.service.xr['update'](biao, {
              id: item.id,
              ds: 'ds',
            });
            while (true) {
              try {
                x = await ctx.service.ai['renderHtmlToImage'](qs);
                // 调用成功，跳出循环
                break;
              } catch (err) {
                console.error('renderHtmlToImage 失败，正在重试...', err);
                // 这里可以加一个短延迟，避免过于频繁地打接口
                await new Promise((r) => setTimeout(r, 500));
              }
            }
            timu = x?.text ?? '';
            text = timu + `\n===================================\n` + text;
            // logger.info(text);

            ai = await ctx.service.ai[serviceMethod](
              text,
              token,
              xone,
              xtwo,
              session_id,
              cookie,
              model,
              key,
            );
          } else {
            await ctx.service.xr['update'](biao, {
              id: item.id,
              ds: 'ds',
            });
            ai = await ctx.service.ai[serviceMethod](
              text,
              token,
              xone,
              xtwo,
              session_id,
              cookie,
              model,
              key,
            );
          }
        }
      } finally {
        // 记录结束时间
        const endTime = Date.now();

        // 计算耗时（毫秒）
        const elapsed = endTime - startTime;

        // 输出耗时
        const minutes = Math.floor(elapsed / 60000);
        const seconds = ((elapsed % 60000) / 1000).toFixed(2);

        console.log(`updatefbkj执行耗时: ${minutes} 分 ${seconds} 秒`);
        if (+minutes === 0 && seconds < 6) {
          logger.error('耗时小于6秒', ai?.status || 401, item.id, item.content);
          await ctx.service.xr['query'](`UPDATE ${biao}
                                         SET ds = '无法回答'
                                         WHERE id = ${item.id}`);
        }

        // console.log(ai);
        try {
          if (ai?.status === 200) {
            await ctx.service.xr['update'](biao, {
              id: item.id,
              ds: ai.data.replace(/```/g, ''),
            });
          } else if (+model !== 10) {
            logger.error('ai状态不是200,model不是10', ai?.status || 401, item.id, item.content);
            await ctx.service.xr['query'](`UPDATE ${biao}
                                           SET ds = null
                                           WHERE id = ${item.id}`);
          } else if (+model === 10) {
            logger.error('ai状态不是200，model是10', ai?.status || 401, item.id, item.content);
            await ctx.service.xr['query'](`UPDATE ${biao}
                                           SET ds = '无法回答'
                                           WHERE id = ${item.id}`);
          } else {
            logger.error('进入你好了', ai?.status || 402, item.id, item.content);
            ctx.service.feishu['fs']('进入你好了:' + item.id);
            await ctx.service.xr['query'](`UPDATE ${biao}
                                           SET ds = '你好'
                                           WHERE id = ${item.id}`);
          }
        } catch (e) {
          const text = e?.response?.data?.error?.message || e?.message || '未知错误';
          ctx.service.feishu['fs']('kj更新出现了' + text);
        } finally {
          const redisSetMap = {
            2: 'sili',
            3: 'zhihuwenda',
            4: 'zhihuwenda1',
            5: 'zhihuwenda2',
            6: 'zhihuwenda2',
            7: 'zhihuwenda3',
            8: 'zhihuwenda4',
            9: 'zhihuwenda5',
            11: 'zhihuwenda6',
            12: 'zhihuwenda7',
          };
          const redisKey = redisSetMap[+model];
          if (redisKey) {
            const isLocked = await app.redis.get(redisKey);
            if (+isLocked !== 3) {
              await app.redis.set(redisKey, '2');
            }
          } else {
            console.log(+model); // 处理未定义的 model 类型
          }
        }

        let kong = await ctx.service.xr['query'](`SELECT count(id) as kong
                                                  FROM ${biao}
                                                  WHERE ds IS NULL
                                                  ORDER BY id desc`);

        let bukong = await ctx.service.xr['query'](`SELECT count(id) as bukong
                                                    FROM ${biao}
                                                    where ds IS not NULL
                                                    ORDER BY id desc`);

        let text1 = `剩余${kong[0].kong}，已完成${bukong[0].bukong}\nper:1，id:${item.id}，model:${+model}\nhttps://vue.wcy9.com/fb/sy?biao=${biao}&id=${allcateid}&type=gz&o=1&f=2\n现在的model是${whatmodel}\n执行耗时: ${minutes} 分 ${seconds} 秒\n${dateNow()}\n${text}\nhttps://vue.wcy9.com/fb/sy?biao=${biao}&id=${allcateid}&type=gz&o=1&f=2`;
        await ctx.service.feishu['fs3'](text1);
      }
    }
  }

  // 在 AiController 类中完善 stopthinkprocess 方法
  async stopthinkprocess() {
    const { ctx, app, logger, service } = this;
    const redisKey = 'zhihuwenda';
    let stop = false;
    // 兼容 application/json 和 x-www-form-urlencoded
    if (
      (ctx.request.body && ctx.request.body.stop === true) ||
      (ctx.request.body && ctx.request.body.stop === 'true')
    ) {
      stop = true;
    }
    let cookie = await app.redis.get(`zhcookie`);
    let recvMessageId = await app.redis.get(`recvMessageId`);

    let result = { success: true, message: '已释放锁' };

    if (stop) {
      // 只处理 model=3
      const config = {
        key: 'zhihuwenda',
        serviceMethod: 'askQuestion3',
        token: 'hf86Hvg9ehsOY0yLuTZc6xGu2bbn7fF9',
        xone: '2.0_tKrURCmBSP3=icg07Cq7TPGcSfcFmRCDgzovjvmzD2g2I7bydz3xWMK=evX42bxh',
        xtwo: '2.0_ReftDnS0cXK9v/tiseVNggO4jUCY91RuF4Z6=t+uamIIbdkLRZifOU0SdBGxMJAs',
        session_id: '3673569108947248099',
        cookie: 'zhcookie',
      };
      try {
        const sendHeaders = {
          accept: '*/*',
          'accept-language': 'zh-CN,zh;q=0.9',
          'cache-control': 'no-cache',
          'content-type': 'text/plain;charset=UTF-8',
          pragma: 'no-cache',
          'x-kl-ajax-request': 'Ajax_Request',
          'x-requested-with': 'fetch',
          'x-xsrftoken': config.token,
          'x-zse-93': '101_3_3.0',
          'x-zse-96': config.xone,
          cookie: cookie,
          Referer: `https://zhida.zhihu.com/search/${config.session_id}`,
        };
        const pollHeaders = {
          ...sendHeaders,
          'x-zse-96': config.xtwo,
        };
        await axios.get(
          `https://zhida.zhihu.com/ai_ingress/ai_chat/polling_message_v2?message_id=${recvMessageId}&stop_signal=true`,
          { headers: pollHeaders },
        );
        result.message = '已优雅停止思考美化并释放锁';
        logger.info('[stopthinkprocess] 已优雅停止思考美化');
      } catch (e) {
        result.success = false;
        result.message = '优雅停止失败，已强制释放锁';
        logger.error('[stopthinkprocess] 调用 askQuestion3 停止失败', e);
      }
    }

    // 无论如何都释放锁
    await app.redis.set(redisKey, '2');
    logger.info('[stopthinkprocess] 已释放 redis 锁 zhihuwenda');
    ctx.body = result;
  }
}

module.exports = AiController;
