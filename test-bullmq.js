/**
 * BullMQ功能测试脚本
 * 独立测试BullMQ配置和功能
 */

const { getBullMQConfig, getQueueConfigByModel } = require('./config/bullmq');

async function testBullMQConfig() {
  console.log('🔧 测试BullMQ配置...');
  
  try {
    // 测试配置获取
    const config = getBullMQConfig('local');
    console.log('✅ BullMQ配置获取成功:', {
      host: config.connection.host,
      port: config.connection.port,
      db: config.connection.db,
    });

    // 测试队列配置
    const queueConfig = getQueueConfigByModel(3);
    console.log('✅ 队列配置获取成功:', queueConfig);

    // 测试无效模型
    const invalidConfig = getQueueConfigByModel(999);
    console.log('✅ 无效模型处理正确:', invalidConfig === null);

  } catch (error) {
    console.error('❌ BullMQ配置测试失败:', error.message);
    return false;
  }

  return true;
}

async function testBullMQConnection() {
  console.log('🔗 测试BullMQ连接...');
  
  try {
    const { Queue } = require('bullmq');
    const { getBullMQConfig } = require('./config/bullmq');
    
    const config = getBullMQConfig('local');
    const queue = new Queue('test-queue', { connection: config.connection });

    // 测试连接
    await queue.add('test-job', { message: 'Hello BullMQ!' });
    console.log('✅ BullMQ连接测试成功');

    // 清理
    await queue.close();
    console.log('✅ 队列连接已关闭');

  } catch (error) {
    console.error('❌ BullMQ连接测试失败:', error.message);
    return false;
  }

  return true;
}

async function testWorkerCreation() {
  console.log('👷 测试Worker创建...');
  
  try {
    const { Worker } = require('bullmq');
    const { getBullMQConfig } = require('./config/bullmq');
    
    const config = getBullMQConfig('local');
    
    const worker = new Worker('test-queue', async (job) => {
      console.log('处理作业:', job.data);
      return { success: true, processed: job.data };
    }, { 
      connection: config.connection,
      concurrency: 1,
    });

    console.log('✅ Worker创建成功');

    // 等待一下然后关闭
    setTimeout(async () => {
      await worker.close();
      console.log('✅ Worker已关闭');
    }, 1000);

  } catch (error) {
    console.error('❌ Worker创建测试失败:', error.message);
    return false;
  }

  return true;
}

async function runAllTests() {
  console.log('🚀 开始BullMQ功能测试\n');

  const tests = [
    { name: 'BullMQ配置测试', fn: testBullMQConfig },
    { name: 'BullMQ连接测试', fn: testBullMQConnection },
    { name: 'Worker创建测试', fn: testWorkerCreation },
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`\n📋 运行: ${test.name}`);
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
        console.log(`✅ ${test.name} 通过`);
      } else {
        console.log(`❌ ${test.name} 失败`);
      }
    } catch (error) {
      console.error(`💥 ${test.name} 异常:`, error.message);
    }
  }

  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！BullMQ配置正确');
    process.exit(0);
  } else {
    console.log('⚠️ 部分测试失败，请检查配置');
    process.exit(1);
  }
}

// 运行测试
runAllTests().catch(error => {
  console.error('💥 测试运行异常:', error);
  process.exit(1);
});
