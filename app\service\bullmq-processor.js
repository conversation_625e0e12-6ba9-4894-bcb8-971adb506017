'use strict';

/**
 * BullMQ处理器服务
 * 提取自updatefbsyzc的核心业务逻辑，移除锁相关代码
 */

const Service = require('egg').Service;
const dayjs = require('dayjs');
const timezone = require('dayjs/plugin/timezone');
const utc = require('dayjs/plugin/utc');

dayjs.extend(utc);
dayjs.extend(timezone);

class BullMQProcessorService extends Service {
  /**
   * 处理单个fbsy记录的核心逻辑
   * @param {object} jobData - 作业数据
   * @param {number} jobData.per - 处理数量限制
   * @param {number} jobData.model - 模型ID
   * @param {boolean} jobData.renewOnly - 是否只处理重新更新的记录
   * @returns {object} 处理结果
   */
  async processItem(jobData) {
    const { ctx, app, logger } = this;
    const { per, model, renewOnly } = jobData;
    const startTime = Date.now();

    try {
      let sql;

      if (renewOnly) {
        // 专门处理ds='重新更新'的记录
        sql = `SELECT * FROM fbsy WHERE ds = '重新更新' ORDER BY id DESC LIMIT ${per}`;
      } else {
        // 兼容原有逻辑（如果需要的话）
        const { type, type1, up } = jobData;
        let option = `allcateid like '%${type}%'`;
        if (type1) {
          option = `(allcateid like '%${type}%' or allcateid like '%${type1}%')`;
        }
        if (+model === 10) {
          option = ` (allcateid like '%656618%' or allcateid like '%656610%' or allcateid like '%656608%' or allcateid like '%656704%')`;
        }

        sql = `SELECT * FROM fbsy WHERE ${option} AND (ds IS NULL OR (ds NOT LIKE '%手稿%' AND ds NOT LIKE '%无法回答%' AND ds NOT LIKE '%无法针对%' AND ds != 'ds')) ORDER BY id DESC LIMIT ${per}`;
        if (up) {
          sql = `SELECT * FROM fbsy WHERE (ds like '%无法回答%' or ds like '%无法针对%') ORDER BY id desc limit ${per}`;
        }
      }

      let res = await ctx.service.xr.query(sql);

      if (!res || res.length === 0) {
        return {
          success: false,
          message: renewOnly ? '没有需要重新更新的记录' : '零',
          data: null,
        };
      }

      const results = [];

      for (let item of res) {
        const itemResult = await this.processSingleItem(item, model, jobData, per, startTime);
        results.push(itemResult);
      }

      return {
        success: true,
        data: results,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      logger.error('BullMQ处理器错误:', error);
      await ctx.service.feishu.fs(`BullMQ处理器错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理单个item的逻辑
   * @param {object} item - 数据库记录
   * @param {number} model - 模型ID
   * @param {object} jobData - 作业数据
   * @param {number} per - 处理数量
   * @param {number} startTime - 开始时间
   * @returns {object} 处理结果
   */
  async processSingleItem(item, model, jobData, per, startTime) {
    const { ctx, app, logger } = this;

    try {
      await ctx.service.feishu.fs3(`【BullMQ】处理item，id=${item.id}`);

      // 构建处理文本
      const text = await this.buildProcessText(item);

      // 获取模型配置
      const config = this.getModelConfig(model);
      if (!config) {
        throw new Error(`不支持的模型类型: ${model}`);
      }

      // 标记为处理中
      await ctx.service.xr.update('fbsy', {
        id: item.id,
        ds: 'ds',
        choice: +model,
      });

      // 调用AI服务
      const ai = await this.callAIService(text, config, model);

      // 处理结果
      const result = await this.handleAIResult(ai, item, model);

      // 记录执行时间和统计信息
      await this.logExecutionStats(item, model, jobData, per, startTime);

      return {
        success: true,
        itemId: item.id,
        aiResult: ai,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      logger.error(`处理item ${item.id} 失败:`, error);

      // 重置ds状态
      await ctx.service.xr.query(
        `UPDATE fbsy SET ds = null, tag = ${+item.tag + 1} WHERE id = ${item.id}`,
      );

      throw error;
    }
  }

  /**
   * 构建处理文本
   */
  async buildProcessText(item) {
    const qiaopi = ''; // 这里可以添加前缀文本

    let text = `
         ${qiaopi}\n
  ${item?.material ? item.material : ''}\n${item.content}\n
  ===========================
   \n${item?.extra ? '这部分是电脑识别的ocr数据，数据可能不准,主要看解析' + item?.extra : ''}\n
  ==========================
  \nA.${item.answerone}\n
  \nB.${item.answertwo}\n
  \nC.${item.answerthree}\n
  \nD.${item.answerfour}\n
  ====================================
  \n答案是${item.answer}\n
  \n ${
    !(item.allcateid?.split(',') || []).some((id) => ['656604'].includes(id.trim()))
      ? `\n解析参考（主要要以你深度思考为主，解析是只是参考）：${item.solution}\n`
      : ''
  }
\n
  一定要多用换行(狂用mathjax的渲染。多点表格进行对比，Markdown 渲染器要求表格前后必须有空行，否则无法正确解析。)，多用emoji丰富回答哟！
 `;

    return text.replace(/fenbike\.cn/g, 'fbstatic.cn').replace(/\/\/fb\./g, 'https://fb.');
  }

  /**
   * 获取模型配置
   */
  getModelConfig(model) {
    const modelMap = {
      2: { key: 'sili', serviceMethod: 'tuili' },
      3: {
        key: 'zhihuwenda',
        serviceMethod: 'askQuestion3',
        token: 'hf86Hvg9ehsOY0yLuTZc6xGu2bbn7fF9',
        xone: '2.0_tKrURCmBSP3=icg07Cq7TPGcSfcFmRCDgzovjvmzD2g2I7bydz3xWMK=evX42bxh',
        xtwo: '2.0_ReftDnS0cXK9v/tiseVNggO4jUCY91RuF4Z6=t+uamIIbdkLRZifOU0SdBGxMJAs',
        session_id: '3673569108947248099',
        cookie: 'zhcookie',
      },
      4: {
        key: 'zhihuwenda1',
        serviceMethod: 'askQuestion3',
        token: 'hf86Hvg9ehsOY0yLuTZc6xGu2bbn7fF9',
        xone: '2.0_tKrURCmBSP3=icg07Cq7TPGcSfcFmRCDgzovjvmzD2g2I7bydz3xWMK=evX42bxh',
        xtwo: '2.0_ReftDnS0cXK9v/tiseVNggO4jUCY91RuF4Z6=t+uamIIbdkLRZifOU0SdBGxMJAs',
        session_id: '3673569108947248099',
        cookie: 'zhcookie',
      },
      // 可以继续添加其他模型配置
    };

    return modelMap[+model];
  }

  /**
   * 调用AI服务
   */
  async callAIService(text, config, model) {
    const { ctx } = this;
    const { serviceMethod, token, xone, xtwo, session_id, cookie } = config;

    return await ctx.service.ai[serviceMethod](
      text,
      token,
      xone,
      xtwo,
      session_id,
      cookie,
      model,
      config.key,
    );
  }

  /**
   * 处理AI结果
   */
  async handleAIResult(ai, item, model) {
    const { ctx, logger } = this;

    if (ai?.status === 200) {
      await ctx.service.xr.update('fbsy', {
        id: item.id,
        ds: ai.data.replace(/```/g, ''),
        choice: null,
      });
      return { success: true, data: ai.data };
    } else if (+model !== 10) {
      logger.error('ai状态不是200,model不是10', ai?.status || 401, item.id, item.content);
      await ctx.service.xr.query(
        `UPDATE fbsy SET ds = null, tag = ${+item.tag + 1} WHERE id = ${item.id}`,
      );
      return { success: false, error: 'AI处理失败', status: ai?.status };
    } else if (+model === 10) {
      logger.error('ai状态不是200，model是10', ai?.status || 401, item.id, item.content);
      await ctx.service.xr.query(`UPDATE fbsy SET ds = '无法回答' WHERE id = ${item.id}`);
      return { success: false, error: 'AI处理失败(model10)', status: ai?.status };
    }
  }

  /**
   * 记录执行统计信息
   */
  async logExecutionStats(item, model, jobData, per, startTime) {
    const { ctx, logger } = this;
    const elapsed = Date.now() - startTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = ((elapsed % 60000) / 1000).toFixed(2);

    logger.info(`BullMQ执行耗时: ${minutes} 分 ${seconds} 秒`);

    // 如果执行时间过短，标记为无法回答
    if (+minutes === 0 && seconds < 6) {
      logger.error('耗时小于6秒', item.id, item.content);
      await ctx.service.xr.query(
        `UPDATE fbsy SET ds = '无法回答', tag = ${+item.tag + 1} WHERE id = ${item.id}`,
      );
    }

    // 发送统计信息到飞书
    await this.sendStatsToFeishu(item, model, jobData, per, minutes, seconds);
  }

  /**
   * 发送统计信息到飞书
   */
  async sendStatsToFeishu(item, model, jobData, per, minutes, seconds) {
    const { ctx } = this;

    if (jobData.renewOnly) {
      // 专门处理重新更新记录的统计
      const renewCount = await ctx.service.xr.query(
        `SELECT count(id) as count FROM fbsy WHERE ds = '重新更新'`,
      );
      const processedCount = await ctx.service.xr.query(
        `SELECT count(id) as count FROM fbsy WHERE ds LIKE '%手稿%'`,
      );

      const text = `【BullMQ-重新更新】剩余${renewCount[0].count}条，已处理${processedCount[0].count}条\nper:${per}，id:${item.id}，model:${+model}\nhttps://vue.wcy9.com/fb/sy?biao=fbsy&type=sy&ids=${item.id}\n执行耗时: ${minutes} 分 ${seconds} 秒\n${new Date().toLocaleString()}`;

      await ctx.service.feishu.fs3(text);
    } else {
      // 原有逻辑（兼容性）
      const { type } = jobData;

      // 获取分类名称
      const getCateNameById = async (id) => {
        const result = await ctx.service.xr.query(`SELECT name FROM fbsycate WHERE id = ${id}`);
        return result[0]?.name || '';
      };

      const cateid = item.allcateid.split(',')[0];
      let typenum = '';
      if (item.allcateid.includes(type)) {
        typenum = await getCateNameById(type);
      } else {
        typenum = cateid;
      }

      // 获取剩余和完成数量
      const option = `allcateid like '%${type}%'`;
      const kong = await ctx.service.xr.query(
        `SELECT count(id) as kong FROM fbsy WHERE ${option} AND (ds IS NULL OR (ds NOT LIKE '%手稿%' AND ds NOT LIKE '%无法回答%' AND ds NOT LIKE '%无法针对%' AND ds != 'ds')) ORDER BY id desc`,
      );
      const bukong = await ctx.service.xr.query(
        `SELECT count(id) as bukong FROM fbsy where ${option} and ds LIKE '%手稿%' ORDER BY id desc`,
      );

      const text = `【BullMQ】${typenum}剩余${kong[0].kong}，已完成${bukong[0].bukong}\nper:${per}，type:${type}，id:${item.id}，model:${+model}\nhttps://vue.wcy9.com/fb/sy?biao=fbsy&type=sy&id=${type}&ids=${item.id}\n执行耗时: ${minutes} 分 ${seconds} 秒\n${new Date().toLocaleString()}`;

      await ctx.service.feishu.fs3(text);
    }
  }
}

module.exports = BullMQProcessorService;
