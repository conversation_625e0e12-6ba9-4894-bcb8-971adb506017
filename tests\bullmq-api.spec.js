const { test, expect } = require('@playwright/test');

/**
 * BullMQ API测试套件
 * 测试update_renew接口和相关功能
 */

// 测试配置
const BASE_URL = 'http://127.0.0.1:7001';
const TEST_HEADERS = {
  'X-White-List': 'playwright-test',
  'User-Agent': 'Playwright Test Runner',
};

test.describe('BullMQ API Tests', () => {
  
  test.beforeAll(async () => {
    console.log('🚀 开始BullMQ API测试');
  });

  test.afterAll(async () => {
    console.log('✅ BullMQ API测试完成');
  });

  test('应该能够获取BullMQ状态', async ({ request }) => {
    const response = await request.get(`${BASE_URL}/bullmqStatus`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('config');
    expect(data).toHaveProperty('workers');
    expect(data).toHaveProperty('timestamp');

    console.log('📊 BullMQ状态:', data);
  });

  test('应该能够切换BullMQ模式', async ({ request }) => {
    // 先获取当前状态
    const statusResponse = await request.get(`${BASE_URL}/toggleBullMQ`, {
      headers: TEST_HEADERS,
    });
    
    expect(statusResponse.status()).toBe(200);
    const currentStatus = await statusResponse.json();
    console.log('📋 当前BullMQ状态:', currentStatus);

    // 切换状态
    const newStatus = !currentStatus.useBullMQ;
    const toggleResponse = await request.get(`${BASE_URL}/toggleBullMQ?enable=${newStatus}`, {
      headers: TEST_HEADERS,
    });

    expect(toggleResponse.status()).toBe(200);
    const toggleData = await toggleResponse.json();
    expect(toggleData).toHaveProperty('success', true);
    expect(toggleData).toHaveProperty('useBullMQ', newStatus);

    console.log(`🔄 BullMQ模式已切换为: ${newStatus ? '启用' : '禁用'}`);

    // 恢复原状态
    await request.get(`${BASE_URL}/toggleBullMQ?enable=${currentStatus.useBullMQ}`, {
      headers: TEST_HEADERS,
    });
  });

  test('应该能够调用update_renew接口', async ({ request }) => {
    // 先启用BullMQ模式
    await request.get(`${BASE_URL}/toggleBullMQ?enable=true`, {
      headers: TEST_HEADERS,
    });

    const response = await request.get(`${BASE_URL}/update_renew?per=1&type=656604&model=3`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('success');
    
    if (data.success) {
      expect(data).toHaveProperty('jobId');
      expect(data).toHaveProperty('queueName');
      expect(data).toHaveProperty('model', 3);
      console.log('✅ update_renew调用成功:', data);
    } else {
      console.log('⚠️ update_renew调用失败:', data.message);
    }
  });

  test('应该能够处理无效的模型参数', async ({ request }) => {
    const response = await request.get(`${BASE_URL}/update_renew?per=1&type=656604&model=999`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('success', false);
    expect(data.message).toContain('不支持的模型类型');

    console.log('✅ 无效模型参数处理正确:', data.message);
  });

  test('应该能够处理缺少模型参数', async ({ request }) => {
    const response = await request.get(`${BASE_URL}/update_renew?per=1&type=656604`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('success', false);
    expect(data.message).toContain('缺少model参数');

    console.log('✅ 缺少参数处理正确:', data.message);
  });

  test('应该能够处理lock参数', async ({ request }) => {
    const response = await request.get(`${BASE_URL}/update_renew?lock=true`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    console.log('✅ lock参数处理完成');
  });

  test('应该能够处理ss参数', async ({ request }) => {
    const response = await request.get(`${BASE_URL}/update_renew?ss=true`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    console.log('✅ ss参数处理完成');
  });

  test('应该能够对比原有接口和新接口', async ({ request }) => {
    console.log('🔄 开始对比测试...');

    // 测试原有接口
    const oldResponse = await request.get(`${BASE_URL}/updatefbsyzc?per=1&type=656604&model=3`, {
      headers: TEST_HEADERS,
    });

    // 测试新接口
    const newResponse = await request.get(`${BASE_URL}/update_renew?per=1&type=656604&model=3`, {
      headers: TEST_HEADERS,
    });

    expect(oldResponse.status()).toBe(200);
    expect(newResponse.status()).toBe(200);

    const oldData = await oldResponse.json();
    const newData = await newResponse.json();

    console.log('📊 原接口响应结构:', Object.keys(oldData));
    console.log('📊 新接口响应结构:', Object.keys(newData));

    // 新接口应该有jobId等BullMQ特有字段
    if (newData.success) {
      expect(newData).toHaveProperty('jobId');
      expect(newData).toHaveProperty('queueName');
    }

    console.log('✅ 接口对比测试完成');
  });

  test('性能测试 - 批量调用', async ({ request }) => {
    console.log('⚡ 开始性能测试...');

    const startTime = Date.now();
    const promises = [];

    // 并发调用10次
    for (let i = 0; i < 10; i++) {
      promises.push(
        request.get(`${BASE_URL}/update_renew?per=1&type=656604&model=3`, {
          headers: TEST_HEADERS,
        })
      );
    }

    const responses = await Promise.all(promises);
    const endTime = Date.now();

    const successCount = responses.filter(r => r.status() === 200).length;
    const totalTime = endTime - startTime;
    const avgTime = totalTime / responses.length;

    console.log(`📈 性能测试结果:`);
    console.log(`   总请求数: ${responses.length}`);
    console.log(`   成功数: ${successCount}`);
    console.log(`   总耗时: ${totalTime}ms`);
    console.log(`   平均耗时: ${avgTime.toFixed(2)}ms`);

    expect(successCount).toBe(responses.length);
    expect(avgTime).toBeLessThan(1000); // 平均响应时间应小于1秒
  });

  test('错误恢复测试', async ({ request }) => {
    console.log('🔧 开始错误恢复测试...');

    // 禁用BullMQ模式
    await request.get(`${BASE_URL}/toggleBullMQ?enable=false`, {
      headers: TEST_HEADERS,
    });

    // 调用update_renew应该仍然能工作（通过原有逻辑）
    const response = await request.get(`${BASE_URL}/update_renew?per=1&type=656604&model=3`, {
      headers: TEST_HEADERS,
    });

    expect(response.status()).toBe(200);
    console.log('✅ 错误恢复测试完成');

    // 重新启用BullMQ模式
    await request.get(`${BASE_URL}/toggleBullMQ?enable=true`, {
      headers: TEST_HEADERS,
    });
  });
});

test.describe('BullMQ Integration Tests', () => {
  
  test('完整流程测试', async ({ request }) => {
    console.log('🔄 开始完整流程测试...');

    // 1. 检查初始状态
    const statusResponse = await request.get(`${BASE_URL}/bullmqStatus`, {
      headers: TEST_HEADERS,
    });
    const status = await statusResponse.json();
    console.log('1️⃣ 初始状态检查完成');

    // 2. 启用BullMQ
    await request.get(`${BASE_URL}/toggleBullMQ?enable=true`, {
      headers: TEST_HEADERS,
    });
    console.log('2️⃣ BullMQ已启用');

    // 3. 提交作业
    const jobResponse = await request.get(`${BASE_URL}/update_renew?per=1&type=656604&model=3`, {
      headers: TEST_HEADERS,
    });
    const jobData = await jobResponse.json();
    console.log('3️⃣ 作业已提交:', jobData.jobId);

    // 4. 等待一段时间让作业处理
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('4️⃣ 等待作业处理完成');

    // 5. 检查最终状态
    const finalStatusResponse = await request.get(`${BASE_URL}/bullmqStatus`, {
      headers: TEST_HEADERS,
    });
    const finalStatus = await finalStatusResponse.json();
    console.log('5️⃣ 最终状态检查完成');

    console.log('✅ 完整流程测试完成');
  });
});
