'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  router.get('/ai', controller.ai['index']);
  router.get('/sili', controller.ai['sili']);
  router.get('/aisave', controller.ai['save']);
  router.get('/aisave1', controller.ai['save1']);
  router.get('/updatefbsyzc', controller.ai['updatefbsyzc']);
  router.get('/updatefbsyzc1', controller.aione['updatefbsyzc']);
  router.post('/zh', controller.ai['ask']);
  router.post('/zhcookie', controller.ai['setzhcookie']);
  router.post('/zhcookie1', controller.ai['setzhcookie1']);
  router.post('/zhcookie2', controller.ai['setzhcookie2']);
  router.post('/zhcookie3', controller.ai['setzhcookie3']);
  router.get('/silistream', controller.ai['silistream']);
  router.get('/silistatus', controller.ai['silistatus']);
  router.get('/doubao', controller.ai['doubao']);
  router.get('/aisw', controller.ai['sw']);
  router.get('/dstotal', controller.ai['dstotal']);
  router.get('/setnull', controller.ai['setnull']);
  router.get('/updatekj', controller.ai['updatekj']);
  router.get('/egg/thinkprocess', controller.ai['thinkprocess']);
  router.get('/timutoextra', controller.ai['timutoextra']);

  // 锁管理路由
  router.get('/check-lock', controller.ai['checkLock']);
  router.get('/release-lock', controller.ai['releaseLock']);

  // 图片识别提取文字路由
  router.post('/extract-text', controller.ai['extractTextFromImage']);
  router.get('/update_renew', controller.ai['update_renew']);

  // BullMQ 队列监控路由
  router.get('/getBullMQQueues', controller.ai['getBullMQQueues']);
  router.post('/pauseBullMQQueue/:queueName', controller.ai['pauseBullMQQueue']);
  router.post('/resumeBullMQQueue/:queueName', controller.ai['resumeBullMQQueue']);
  router.post('/drainBullMQQueue/:queueName', controller.ai['drainBullMQQueue']);
};
