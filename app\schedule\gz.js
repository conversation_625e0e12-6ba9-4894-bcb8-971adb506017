const axios = require('axios');
module.exports = {
  schedule: {
    interval: '2s', // 1 分钟间隔
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['prod'],
    immediate: true,
    enabled: true,
  },
  async task(ctx) {
    // 从配置文件获取服务器信息
    const config = ctx.app.config;
    const serverConfig = config.server || {};

    // 检查是否为广州服务器且启用了AI任务
    const isGzServer = serverConfig.location === 'gz';
    const aiTaskEnabled = serverConfig.features?.aiTask === true;

    // 记录服务器信息用于调试
    ctx.logger.info(
      `服务器位置: ${serverConfig.location}, AI任务启用: ${aiTaskEnabled}, 执行条件满足: ${isGzServer && aiTaskEnabled}`,
    );

    // 只有广州服务器且启用AI任务时才执行
    if (isGzServer && aiTaskEnabled) {
      let res = await ctx.service.xr.query(`select *
                                            from sw
                                            where name = 'ai'`);
      const sw = Number(res[0]?.sw || 0);
      ctx.logger.info('sw 的值:', sw);
      if (+sw !== 0) {
        // 检查是否启用BullMQ
        const useBullMQ = serverConfig.features?.useBullMQ === true;

        if (useBullMQ) {
          // 使用BullMQ方式
          ctx.logger.info('[定时任务] 使用BullMQ方式处理');
          try {
            const response = await axios.get(
              'http://127.0.0.1:7001/update_renew?per=1&type=656604&model=3',
              {
                headers: {
                  'X-White-List': 'egg-schedule',
                  'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                },
              },
            );
            ctx.logger.info(
              `[定时任务] BullMQ请求成功, status=${response.status}, jobId=${response.data?.jobId}`,
            );
          } catch (e) {
            ctx.logger.error(`[定时任务] BullMQ请求异常: ${e.message}`);
            try {
              await ctx.service.feishu.fs3(`[定时任务] BullMQ方式异常: ${e.message}`);
            } catch (feishuErr) {
              ctx.logger.error(`[定时任务] 飞书通知失败: ${feishuErr.message}`);
            }
          }
        } else {
          // 使用原有方式
          ctx.logger.info('[定时任务] 使用原有方式处理');
          const data = ['updatefbsyzc?per=1&type=656604&model=3', ''];
          for (let i in data) {
            ctx.logger.info(`[定时任务] 开始请求 index=${i}, url=${data[i]}`);
            try {
              const response = await axios.get('http://127.0.0.1:7001/' + data[i], {
                headers: {
                  'X-White-List': 'egg-schedule',
                  'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                },
              });
              ctx.logger.info(
                `[定时任务] 请求成功 index=${i}, url=${data[i]}, status=${response.status}`,
              );
            } catch (e) {
              ctx.logger.error(
                `[定时任务] 请求异常 index=${i}, url=${data[i]}, error=${e.message}`,
              );
              try {
                await ctx.service.feishu.fs3(`[定时任务] ${data[i]} 异常: ${e.message}`);
              } catch (feishuErr) {
                ctx.logger.error(`[定时任务] 飞书通知失败: ${feishuErr.message}`);
              }
            }
          }
        }
      }
    }

    // await axios.get("http://127.0.0.1:7001/xccf").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xchl").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdaxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdaxmch").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdahfhl").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xmairxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xmairhfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/hfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lzxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sda").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lyxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lyhfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lylzxm").then(() => {});
  },
};
