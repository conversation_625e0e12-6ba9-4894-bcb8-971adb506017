// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Playwright配置文件
 * 用于BullMQ API测试
 */

module.exports = defineConfig({
  testDir: './tests',
  /* 并行运行测试 */
  fullyParallel: false, // BullMQ测试需要顺序执行
  /* 失败时不重试 */
  retries: 0,
  /* 测试超时时间 */
  timeout: 30000,
  /* 期望超时时间 */
  expect: {
    timeout: 5000,
  },
  /* 在CI中禁用并行 */
  workers: 1,
  /* 报告器配置 */
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['list'],
  ],
  /* 全局设置 */
  use: {
    /* 基础URL */
    baseURL: 'http://127.0.0.1:7001',
    /* 收集失败时的trace */
    trace: 'on-first-retry',
    /* 截图设置 */
    screenshot: 'only-on-failure',
    /* 视频设置 */
    video: 'retain-on-failure',
    /* 额外的HTTP头 */
    extraHTTPHeaders: {
      'X-Test-Source': 'Playwright',
    },
  },

  /* 项目配置 */
  projects: [
    {
      name: 'API Tests',
      testMatch: '**/*.spec.js',
      use: {
        ...devices['Desktop Chrome'],
      },
    },
  ],

  /* 本地开发服务器 */
  webServer: {
    command: 'pnpm dev',
    url: 'http://127.0.0.1:7001',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000, // 2分钟启动超时
  },
});
