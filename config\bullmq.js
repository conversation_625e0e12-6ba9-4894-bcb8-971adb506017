'use strict';

/**
 * BullMQ配置模块
 * 基于现有Redis配置，为BullMQ提供队列和Worker配置
 */

const { getRedisConfig } = require('./redis');

/**
 * 获取BullMQ Redis连接配置
 * @param {string} env - 环境名称
 * @returns {object} BullMQ兼容的Redis连接配置
 */
function getBullMQRedisConfig(env = 'local') {
  const redisConfig = getRedisConfig(env);

  // 转换为BullMQ兼容的格式
  return {
    host: redisConfig.client.host,
    port: redisConfig.client.port,
    password: redisConfig.client.password,
    db: parseInt(redisConfig.client.db),
    connectTimeout: redisConfig.client.connectTimeout,
    maxRetriesPerRequest: redisConfig.client.maxRetriesPerRequest,
    retryDelayOnFailover: redisConfig.client.retryDelayOnFailover,
    enableReadyCheck: redisConfig.client.enableReadyCheck,
    lazyConnect: redisConfig.client.lazyConnect,
  };
}

/**
 * 队列配置映射
 * 为不同的模型定义队列名称和配置
 */
const queueConfigs = {
  // 模型2: 硅基流动
  sili: {
    name: 'sili-queue',
    concurrency: 1,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    removeOnComplete: 10,
    removeOnFail: 5,
    // 重试策略
    retry: {
      attempts: 3,
      delay: 2000,
      backoff: 'exponential',
    },
    // 失败处理
    failed: {
      maxAge: 24 * 3600 * 1000, // 24小时后删除失败作业
      count: 100, // 最多保留100个失败作业
    },
  },
  // 模型3: 知乎问答
  zhihu: {
    name: 'zhihu-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型4: 知乎问答1
  zhihu1: {
    name: 'zhihu1-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型5: 知乎问答2
  zhihu2: {
    name: 'zhihu2-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型6: 知乎问答2 (重复)
  zhihu2_alt: {
    name: 'zhihu2-alt-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型7: 知乎问答3
  zhihu3: {
    name: 'zhihu3-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型8: 知乎问答4
  zhihu4: {
    name: 'zhihu4-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型9: 知乎问答5
  zhihu5: {
    name: 'zhihu5-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型11: 知乎问答6
  zhihu6: {
    name: 'zhihu6-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
  // 模型12: 知乎问答7
  zhihu7: {
    name: 'zhihu7-queue',
    concurrency: 1,
    attempts: 3,
    backoff: 'exponential',
    removeOnComplete: 10,
    removeOnFail: 5,
  },
};

/**
 * 模型到队列的映射
 */
const modelToQueue = {
  2: 'sili',
  3: 'zhihu',
  4: 'zhihu1',
  5: 'zhihu2',
  6: 'zhihu2_alt',
  7: 'zhihu3',
  8: 'zhihu4',
  9: 'zhihu5',
  11: 'zhihu6',
  12: 'zhihu7',
};

/**
 * 获取指定模型的队列配置
 * @param {number} model - 模型编号
 * @returns {object|null} 队列配置
 */
function getQueueConfigByModel(model) {
  const queueKey = modelToQueue[model];
  return queueKey ? queueConfigs[queueKey] : null;
}

/**
 * 获取所有队列配置
 * @returns {object} 所有队列配置
 */
function getAllQueueConfigs() {
  return queueConfigs;
}

/**
 * 获取BullMQ完整配置
 * @param {string} env - 环境名称
 * @returns {object} BullMQ配置对象
 */
function getBullMQConfig(env = 'local') {
  const connection = getBullMQRedisConfig(env);

  return {
    connection,
    queueConfigs,
    modelToQueue,
    defaultJobOptions: {
      removeOnComplete: 10,
      removeOnFail: 5,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
  };
}

module.exports = {
  getBullMQRedisConfig,
  getBullMQConfig,
  queueConfigs,
  modelToQueue,
  getQueueConfigByModel,
  getAllQueueConfigs,
};
