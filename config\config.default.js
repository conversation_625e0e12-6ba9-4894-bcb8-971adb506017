/* eslint valid-jsdoc: "off" */

'use strict';

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */

const path = require('node:path');
module.exports = (appInfo) => {
  /**
   * built-in config
   * @type {Egg.EggAppConfig}
   **/
  const config = (exports = {});
  config.vps = 'gz';

  config.proxy = true;
  config.assets = {
    publicPath: '/public/',
  };

  // 启动优化：模块懒加载配置
  config.lazyLoading = {
    enabled: true, // 启用懒加载
    preloadModules: [], // 预加载模块列表（可选）
    heavyModules: [
      'puppeteer',
      'sharp',
      'openai',
      'cheerio',
      'tencentcloud-sdk-nodejs',
      'form-data',
      'axios',
      'yaml',
    ], // 重型模块列表
  };

  // 启动优化：集群配置
  config.cluster = {
    listen: {
      port: 7001,
      hostname: '127.0.0.1', // 开发环境本地访问
    },
    workers: 1, // 开发环境使用单进程
  };

  // 启动优化：减少文件监听范围
  config.watcher = {
    // 仅监听必要目录
    directories: ['app/controller', 'app/service', 'app/router.js', 'config'],
    // 忽略不必要的文件和目录
    ignore: [
      '**/node_modules/**',
      '**/*.log',
      '**/logs/**',
      '**/run/**',
      '**/typings/**',
      'app/public/**',
      'app/view/**',
    ],
  };
  config.multipart = {
    mode: 'file',
  };

  // 增加请求体大小限制，支持大型画布数据
  config.bodyParser = {
    jsonLimit: '20mb', // JSON请求体限制为20MB
    formLimit: '20mb', // 表单请求体限制为20MB
    textLimit: '20mb', // 文本请求体限制为20MB
    enableTypes: ['json', 'form', 'text'],
  };
  config.io = {
    init: {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    }, // 默认参数
    namespace: {
      '/button': {
        connectionMiddleware: [], // 如果需要，可添加中间件
        packetMiddleware: [],
      },
      '/thinkprocess': {
        connectionMiddleware: [], // 如果需要，可添加中间件
        packetMiddleware: [],
      },
      '/test': {
        connectionMiddleware: [], // 如果需要，可添加中间件
        packetMiddleware: [],
      },
      '/canvas': {
        connectionMiddleware: [], // 🎨 画布实时传输命名空间
        packetMiddleware: [],
      },
    },
  };
  // 启动优化：禁用开发环境的定时任务
  config.schedule = {
    env: ['production', 'prod'],
    // 开发环境下禁用所有定时任务以加快启动
    disable: process.env.NODE_ENV !== 'production',
  };
  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + '_1618835746568_1066';
  config.view = {
    defaultViewEngine: 'nunjucks',
    mapping: {
      html: 'nunjucks',
    },
    // 这里将 autoescape 配置放在了 view 中
    autoescape: true,
  };
  config.nunjucks = {
    tags: {
      blockStart: '{%',
      blockEnd: '%}',
      variableStart: '{[',
      variableEnd: ']}',
      commentStart: '{#',
      commentEnd: '#}',
    },
  };
  config.news = {
    pageSize: 5,
    serverUrl: 'https://hacker-news.firebaseio.com/v0',
  };

  config.security = {
    csrf: {
      enable: false,
    },
  };

  // add your middleware config here
  config.middleware = ['robot', 'logger'];
  exports.robot = {
    ua: [/Baiduspider/i],
  };
  // add your user config here
  const userConfig = {
    // myAppName: 'egg',
  };
  // 启动优化：调整日志级别
  exports.logger = {
    level: process.env.NODE_ENV === 'production' ? 'INFO' : 'WARN',
    consoleLevel: process.env.NODE_ENV === 'production' ? 'INFO' : 'WARN',
    disableConsoleAfterReady: process.env.NODE_ENV === 'production', // 生产环境启动后禁用控制台输出
    dir: '/root/eggjslogs',
    allowDebugAtProd: true,
    // 启动优化：减少日志输出
    buffer: true, // 启用日志缓冲
    encoding: 'utf8',
  };
  exports.logrotator = {
    filesRotateBySize: [path.join(appInfo.root, 'logs', appInfo.name, 'egg-web.log')],
    maxFileSize: 2 * 1024 * 1024 * 1024,
  };
  exports.cors = {
    origin: '*',
    // 表示允许的源
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
    // 表示允许的http请求方式
  };
  config.customLogger = {
    scheduleLogger: {
      // consoleLevel:'none',
      // file: path.join(appInfo.root, 'logs', appInfo.name, 'egg-schedule.log'),
    },
  };
  // 登录帐号密码配置
  config.login = {
    username: 'nihao',
    password: 'nihao', // 为空
  };

  // BullMQ配置
  config.bullmq = {
    enable: true, // 是否启用BullMQ
    required: false, // BullMQ失败是否阻止应用启动
  };

  return {
    ...config,
    ...userConfig,
  };
};
